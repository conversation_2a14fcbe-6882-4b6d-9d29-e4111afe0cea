"""Analytics and reporting state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_

from ..models.whatsapp import Message, Campaign, WhatsAppAccount
from ..models.bot import BotTest, BotTestResult
from ..models.analytics import MessageAnalytics, CampaignAnalytics
from ..database import get_session
from .auth_state import AuthState


class AnalyticsState(AuthState):
    """Analytics and reporting state management."""
    
    # Date range selection
    date_range: str = "7_days"
    start_date: str = ""
    end_date: str = ""
    
    # Analytics data
    message_stats: Dict[str, Any] = {}
    campaign_stats: Dict[str, Any] = {}
    bot_test_stats: Dict[str, Any] = {}
    
    # Charts data
    message_volume_chart: List[Dict[str, Any]] = []
    delivery_rate_chart: List[Dict[str, Any]] = []
    message_type_chart: List[Dict[str, Any]] = []
    campaign_performance_chart: List[Dict[str, Any]] = []
    bot_performance_chart: List[Dict[str, Any]] = []
    
    # Top performers
    top_campaigns: List[Dict[str, Any]] = []
    top_templates: List[Dict[str, Any]] = []
    top_bot_configs: List[Dict[str, Any]] = []
    
    # Real-time metrics
    messages_today: int = 0
    delivery_rate_today: float = 0.0
    active_campaigns: int = 0
    bot_tests_today: int = 0
    
    # UI states
    loading: bool = False
    error_message: str = ""
    selected_metric: str = "messages"
    
    # Export options
    export_format: str = "csv"
    export_loading: bool = False
    
    def get_date_range(self) -> tuple:
        """Get start and end dates based on selected range."""
        end_date = datetime.now()
        
        if self.date_range == "today":
            start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
        elif self.date_range == "7_days":
            start_date = end_date - timedelta(days=7)
        elif self.date_range == "30_days":
            start_date = end_date - timedelta(days=30)
        elif self.date_range == "90_days":
            start_date = end_date - timedelta(days=90)
        elif self.date_range == "custom":
            start_date = datetime.fromisoformat(self.start_date) if self.start_date else end_date - timedelta(days=7)
            end_date = datetime.fromisoformat(self.end_date) if self.end_date else end_date
        else:
            start_date = end_date - timedelta(days=7)
        
        return start_date, end_date
    
    async def load_analytics_data(self):
        """Load all analytics data."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            start_date, end_date = self.get_date_range()
            
            with get_session() as session:
                # Get user's WhatsApp accounts
                accounts = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).all()
                
                account_ids = [account.id for account in accounts]
                
                if account_ids:
                    await self._load_message_analytics(session, account_ids, start_date, end_date)
                    await self._load_campaign_analytics(session, account_ids, start_date, end_date)
                    await self._load_bot_analytics(session, start_date, end_date)
                    await self._load_real_time_metrics(session, account_ids)
                    await self._load_chart_data(session, account_ids, start_date, end_date)
                
        except Exception as e:
            self.error_message = f"Failed to load analytics: {str(e)}"
        finally:
            self.loading = False
    
    async def _load_message_analytics(self, session, account_ids: List[int], start_date: datetime, end_date: datetime):
        """Load message analytics."""
        # Total messages
        total_messages = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        # Messages by status
        sent_messages = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "sent",
            Message.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        delivered_messages = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "delivered",
            Message.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        read_messages = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "read",
            Message.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        failed_messages = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "failed",
            Message.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        # Calculate rates
        delivery_rate = (delivered_messages / sent_messages * 100) if sent_messages > 0 else 0
        read_rate = (read_messages / delivered_messages * 100) if delivered_messages > 0 else 0
        failure_rate = (failed_messages / total_messages * 100) if total_messages > 0 else 0
        
        self.message_stats = {
            "total_messages": total_messages,
            "sent_messages": sent_messages,
            "delivered_messages": delivered_messages,
            "read_messages": read_messages,
            "failed_messages": failed_messages,
            "delivery_rate": round(delivery_rate, 2),
            "read_rate": round(read_rate, 2),
            "failure_rate": round(failure_rate, 2)
        }
    
    async def _load_campaign_analytics(self, session, account_ids: List[int], start_date: datetime, end_date: datetime):
        """Load campaign analytics."""
        # Total campaigns
        total_campaigns = session.query(func.count(Campaign.id)).filter(
            Campaign.account_id.in_(account_ids),
            Campaign.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        # Campaign statuses
        completed_campaigns = session.query(func.count(Campaign.id)).filter(
            Campaign.account_id.in_(account_ids),
            Campaign.status == "completed",
            Campaign.created_at.between(start_date, end_date)
        ).scalar() or 0
        
        running_campaigns = session.query(func.count(Campaign.id)).filter(
            Campaign.account_id.in_(account_ids),
            Campaign.status == "running"
        ).scalar() or 0
        
        # Campaign performance
        campaign_results = session.query(
            func.sum(Campaign.messages_sent),
            func.sum(Campaign.messages_failed),
            func.sum(Campaign.total_recipients)
        ).filter(
            Campaign.account_id.in_(account_ids),
            Campaign.created_at.between(start_date, end_date)
        ).first()
        
        total_sent = campaign_results[0] or 0
        total_failed = campaign_results[1] or 0
        total_recipients = campaign_results[2] or 0
        
        success_rate = ((total_sent - total_failed) / total_sent * 100) if total_sent > 0 else 0
        
        self.campaign_stats = {
            "total_campaigns": total_campaigns,
            "completed_campaigns": completed_campaigns,
            "running_campaigns": running_campaigns,
            "total_recipients": total_recipients,
            "total_sent": total_sent,
            "total_failed": total_failed,
            "success_rate": round(success_rate, 2)
        }
        
        # Top performing campaigns
        self.top_campaigns = [
            {
                "name": campaign.name,
                "recipients": campaign.total_recipients,
                "sent": campaign.messages_sent,
                "success_rate": round((campaign.messages_sent - campaign.messages_failed) / campaign.messages_sent * 100, 2) if campaign.messages_sent > 0 else 0
            }
            for campaign in session.query(Campaign).filter(
                Campaign.account_id.in_(account_ids),
                Campaign.created_at.between(start_date, end_date),
                Campaign.messages_sent > 0
            ).order_by(Campaign.messages_sent.desc()).limit(5).all()
        ]
    
    async def _load_bot_analytics(self, session, start_date: datetime, end_date: datetime):
        """Load bot testing analytics."""
        # Get user's bot configurations
        from ..models.bot import BotConfiguration
        
        bot_configs = session.query(BotConfiguration).filter(
            BotConfiguration.user_id == self.current_user["id"],
            BotConfiguration.is_active == True
        ).all()
        
        config_ids = [config.id for config in bot_configs]
        
        if config_ids:
            # Total bot tests
            total_tests = session.query(func.count(BotTest.id)).filter(
                BotTest.configuration_id.in_(config_ids),
                BotTest.created_at.between(start_date, end_date)
            ).scalar() or 0
            
            # Completed tests
            completed_tests = session.query(func.count(BotTest.id)).filter(
                BotTest.configuration_id.in_(config_ids),
                BotTest.status == "completed",
                BotTest.created_at.between(start_date, end_date)
            ).scalar() or 0
            
            # Average success rate
            avg_success_rate = session.query(func.avg(BotTestResult.success_rate)).filter(
                BotTestResult.test_id.in_(
                    session.query(BotTest.id).filter(
                        BotTest.configuration_id.in_(config_ids),
                        BotTest.created_at.between(start_date, end_date)
                    )
                )
            ).scalar() or 0
            
            # Average response time
            avg_response_time = session.query(func.avg(BotTestResult.average_response_time)).filter(
                BotTestResult.test_id.in_(
                    session.query(BotTest.id).filter(
                        BotTest.configuration_id.in_(config_ids),
                        BotTest.created_at.between(start_date, end_date)
                    )
                )
            ).scalar() or 0
            
            self.bot_test_stats = {
                "total_tests": total_tests,
                "completed_tests": completed_tests,
                "completion_rate": round(completed_tests / total_tests * 100, 2) if total_tests > 0 else 0,
                "avg_success_rate": round(float(avg_success_rate), 2) if avg_success_rate else 0,
                "avg_response_time": round(float(avg_response_time), 2) if avg_response_time else 0
            }
            
            # Top performing bot configs
            self.top_bot_configs = [
                {
                    "name": config.bot_name,
                    "tests_run": session.query(func.count(BotTest.id)).filter(
                        BotTest.configuration_id == config.id,
                        BotTest.created_at.between(start_date, end_date)
                    ).scalar() or 0,
                    "avg_success_rate": round(float(session.query(func.avg(BotTestResult.success_rate)).filter(
                        BotTestResult.test_id.in_(
                            session.query(BotTest.id).filter(BotTest.configuration_id == config.id)
                        )
                    ).scalar() or 0), 2)
                }
                for config in bot_configs
            ]
        else:
            self.bot_test_stats = {
                "total_tests": 0,
                "completed_tests": 0,
                "completion_rate": 0,
                "avg_success_rate": 0,
                "avg_response_time": 0
            }
            self.top_bot_configs = []
    
    async def _load_real_time_metrics(self, session, account_ids: List[int]):
        """Load real-time metrics for today."""
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Messages today
        self.messages_today = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.created_at >= today
        ).scalar() or 0
        
        # Delivery rate today
        sent_today = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "sent",
            Message.created_at >= today
        ).scalar() or 0
        
        delivered_today = session.query(func.count(Message.id)).filter(
            Message.account_id.in_(account_ids),
            Message.status == "delivered",
            Message.created_at >= today
        ).scalar() or 0
        
        self.delivery_rate_today = round(delivered_today / sent_today * 100, 2) if sent_today > 0 else 0
        
        # Active campaigns
        self.active_campaigns = session.query(func.count(Campaign.id)).filter(
            Campaign.account_id.in_(account_ids),
            Campaign.status.in_(["running", "scheduled"])
        ).scalar() or 0
        
        # Bot tests today
        from ..models.bot import BotConfiguration
        
        config_ids = [
            config.id for config in session.query(BotConfiguration).filter(
                BotConfiguration.user_id == self.current_user["id"]
            ).all()
        ]
        
        if config_ids:
            self.bot_tests_today = session.query(func.count(BotTest.id)).filter(
                BotTest.configuration_id.in_(config_ids),
                BotTest.created_at >= today
            ).scalar() or 0
        else:
            self.bot_tests_today = 0
    
    async def _load_chart_data(self, session, account_ids: List[int], start_date: datetime, end_date: datetime):
        """Load data for charts."""
        # Message volume over time (daily)
        days = []
        current_date = start_date
        while current_date <= end_date:
            day_start = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            message_count = session.query(func.count(Message.id)).filter(
                Message.account_id.in_(account_ids),
                Message.created_at.between(day_start, day_end)
            ).scalar() or 0
            
            days.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "messages": message_count
            })
            
            current_date += timedelta(days=1)
        
        self.message_volume_chart = days
        
        # Message types distribution
        message_types = session.query(
            Message.message_type,
            func.count(Message.id)
        ).filter(
            Message.account_id.in_(account_ids),
            Message.created_at.between(start_date, end_date)
        ).group_by(Message.message_type).all()
        
        self.message_type_chart = [
            {
                "type": msg_type,
                "count": count
            }
            for msg_type, count in message_types
        ]
    
    def set_date_range(self, range_type: str):
        """Set date range for analytics."""
        self.date_range = range_type
        self.error_message = ""
    
    def set_start_date(self, date: str):
        """Set custom start date."""
        self.start_date = date
        if self.date_range != "custom":
            self.date_range = "custom"
    
    def set_end_date(self, date: str):
        """Set custom end date."""
        self.end_date = date
        if self.date_range != "custom":
            self.date_range = "custom"
    
    def clear_error(self):
        """Clear error message."""
        self.error_message = ""
