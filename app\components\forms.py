"""Form components for the application."""

import reflex as rx


def form_field(
    label: str,
    input_component: rx.Component,
    error: str = "",
    required: bool = False,
    help_text: str = "",
    **kwargs
) -> rx.Component:
    """Reusable form field component."""
    return rx.vstack(
        rx.hstack(
            rx.text(label, size="sm", weight="medium"),
            rx.cond(
                required,
                rx.text("*", color="red", size="sm")
            ),
            spacing="1"
        ),
        input_component,
        rx.cond(
            help_text,
            rx.text(help_text, size="xs", color="gray")
        ),
        rx.cond(
            error,
            rx.text(error, size="xs", color="red")
        ),
        spacing="1",
        width="100%",
        **kwargs
    )
