"""Layout components for the application."""

import reflex as rx
from typing import Any
from ..state.auth_state import AuthState
from .navigation import sidebar, navbar


def layout(content: rx.Component, **kwargs) -> rx.Component:
    """Basic layout wrapper."""
    return rx.box(
        content,
        width="100%",
        min_height="100vh",
        bg="gray.50",
        **kwargs
    )


def protected_layout(content: rx.Component, current_page: str = "") -> rx.Component:
    """Protected layout with authentication check and navigation."""
    return rx.cond(
        AuthState.is_authenticated,
        rx.box(
            # Main layout with sidebar and content
            rx.hstack(
                # Sidebar
                sidebar(current_page=current_page),
                
                # Main content area
                rx.box(
                    # Top navbar
                    navbar(),
                    
                    # Page content
                    rx.box(
                        content,
                        padding="6",
                        width="100%"
                    ),
                    
                    width="100%",
                    min_height="100vh",
                    bg="gray.50"
                ),
                
                spacing="0",
                width="100%",
                align="start"
            ),
            width="100%",
            min_height="100vh"
        ),
        # Redirect to login if not authenticated
        rx.redirect("/login")
    )


def admin_layout(content: rx.Component, current_page: str = "") -> rx.Component:
    """Admin layout with additional admin checks."""
    return rx.cond(
        AuthState.is_authenticated,
        rx.cond(
            AuthState.current_user.get("is_admin", False) if AuthState.current_user else False,
            protected_layout(content, current_page),
            rx.center(
                rx.card(
                    rx.vstack(
                        rx.icon("shield-x", size=48, color="red"),
                        rx.heading("Access Denied", size="lg"),
                        rx.text("You don't have permission to access this page."),
                        rx.button(
                            "Go to Dashboard",
                            on_click=lambda: rx.redirect("/dashboard"),
                            color_scheme="blue"
                        ),
                        spacing="4",
                        align="center"
                    ),
                    padding="8"
                ),
                min_height="100vh"
            )
        ),
        rx.redirect("/login")
    )


def card_layout(
    title: str,
    content: rx.Component,
    actions: rx.Component = None,
    **kwargs
) -> rx.Component:
    """Reusable card layout with title and optional actions."""
    return rx.card(
        rx.vstack(
            # Card header
            rx.hstack(
                rx.heading(title, size="md"),
                rx.spacer(),
                actions if actions else rx.box(),
                width="100%",
                justify="between",
                align="center"
            ),
            
            # Card content
            content,
            
            spacing="4",
            width="100%",
            align="start"
        ),
        padding="6",
        width="100%",
        **kwargs
    )


def stats_card(
    title: str,
    value: Any,
    subtitle: str = "",
    icon: str = "bar-chart",
    color: str = "blue",
    **kwargs
) -> rx.Component:
    """Reusable stats card component."""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.icon(icon, size=20, color=color),
                rx.text(title, size="sm", color="gray"),
                spacing="2",
                align="center"
            ),
            rx.text(
                str(value),
                size="xl",
                weight="bold"
            ),
            rx.cond(
                subtitle,
                rx.text(
                    subtitle,
                    size="xs",
                    color="gray"
                )
            ),
            spacing="2",
            align="start"
        ),
        padding="4",
        **kwargs
    )


def empty_state(
    title: str,
    description: str,
    icon: str = "inbox",
    action_text: str = "",
    action_href: str = "",
    **kwargs
) -> rx.Component:
    """Empty state component for when there's no data."""
    return rx.center(
        rx.vstack(
            rx.icon(icon, size=48, color="gray"),
            rx.heading(title, size="lg", color="gray"),
            rx.text(description, color="gray", text_align="center"),
            rx.cond(
                action_text and action_href,
                rx.button(
                    action_text,
                    on_click=lambda: rx.redirect(action_href),
                    color_scheme="blue",
                    variant="outline"
                )
            ),
            spacing="4",
            align="center",
            max_width="400px"
        ),
        min_height="300px",
        width="100%",
        **kwargs
    )


def loading_spinner(text: str = "Loading...") -> rx.Component:
    """Loading spinner component."""
    return rx.center(
        rx.vstack(
            rx.spinner(size="lg"),
            rx.text(text, color="gray"),
            spacing="4",
            align="center"
        ),
        min_height="200px",
        width="100%"
    )


def error_message(
    title: str = "Something went wrong",
    description: str = "Please try again later",
    retry_action: Any = None
) -> rx.Component:
    """Error message component."""
    return rx.center(
        rx.card(
            rx.vstack(
                rx.icon("alert-circle", size=48, color="red"),
                rx.heading(title, size="lg"),
                rx.text(description, color="gray", text_align="center"),
                rx.cond(
                    retry_action,
                    rx.button(
                        "Try Again",
                        on_click=retry_action,
                        color_scheme="red",
                        variant="outline"
                    )
                ),
                spacing="4",
                align="center"
            ),
            padding="8",
            max_width="400px"
        ),
        min_height="300px",
        width="100%"
    )


def notification_toast(
    message: str,
    type: str = "info",
    **kwargs
) -> rx.Component:
    """Notification toast component."""
    color_map = {
        "success": "green",
        "error": "red", 
        "warning": "orange",
        "info": "blue"
    }
    
    icon_map = {
        "success": "check-circle",
        "error": "x-circle",
        "warning": "alert-triangle", 
        "info": "info"
    }
    
    return rx.callout(
        message,
        icon=icon_map.get(type, "info"),
        color_scheme=color_map.get(type, "blue"),
        **kwargs
    )


def page_header(
    title: str,
    description: str = "",
    actions: rx.Component = None,
    breadcrumbs: list = None
) -> rx.Component:
    """Page header component with title, description, and actions."""
    return rx.vstack(
        # Breadcrumbs
        rx.cond(
            breadcrumbs,
            rx.hstack(
                rx.foreach(
                    breadcrumbs,
                    lambda crumb: rx.hstack(
                        rx.link(crumb["label"], href=crumb.get("href", "#")),
                        rx.cond(
                            crumb != breadcrumbs[-1],
                            rx.icon("chevron-right", size=16, color="gray")
                        ),
                        spacing="2"
                    )
                ),
                spacing="1",
                align="center"
            )
        ),
        
        # Title and actions
        rx.hstack(
            rx.vstack(
                rx.heading(title, size="xl"),
                rx.cond(
                    description,
                    rx.text(description, color="gray")
                ),
                spacing="1",
                align="start"
            ),
            rx.spacer(),
            actions if actions else rx.box(),
            width="100%",
            justify="between",
            align="start"
        ),
        
        spacing="3",
        width="100%",
        align="start"
    )
