"""Template management pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.template_state import TemplateState


def template_card(template: dict) -> rx.Component:
    """Template card component."""
    status_color = {
        "APPROVED": "green",
        "PENDING": "yellow",
        "REJECTED": "red",
        "DRAFT": "gray"
    }.get(template["status"], "gray")

    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(template["template_name"], size="md"),
                    rx.text(template["category"], color="gray", size="sm"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.badge(template["status"], color_scheme=status_color),
                align="center",
                width="100%"
            ),

            rx.text(
                template["body_text"][:100] + "..." if len(template["body_text"]) > 100 else template["body_text"],
                color="gray",
                size="sm"
            ),

            rx.hstack(
                rx.text(f"Language: {template['language']}", size="xs", color="gray"),
                rx.text(f"Account: {template['account_name']}", size="xs", color="gray"),
                spacing="4"
            ),

            rx.hstack(
                rx.button(
                    "View",
                    on_click=TemplateState.select_template(template["id"]),
                    size="sm",
                    variant="outline"
                ),
                rx.button(
                    "Delete",
                    on_click=TemplateState.delete_template(template["id"]),
                    size="sm",
                    color_scheme="red",
                    variant="outline"
                ),
                spacing="2"
            ),

            align="start",
            spacing="3",
            width="100%"
        ),
        width="100%"
    )


def template_creation_form() -> rx.Component:
    """Template creation form."""
    return rx.card(
        rx.vstack(
            rx.heading("Create New Template", size="lg"),

            # Basic info
            rx.grid(
                rx.vstack(
                    rx.text("Template Name:", weight="bold"),
                    rx.input(
                        placeholder="my_template_name",
                        value=TemplateState.template_name,
                        on_change=TemplateState.set_template_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("Language:", weight="bold"),
                    rx.select(
                        ["en_US", "es_ES", "fr_FR", "de_DE", "pt_BR"],
                        value=TemplateState.template_language,
                        on_change=TemplateState.set_template_language,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("Category:", weight="bold"),
                    rx.select(
                        ["MARKETING", "UTILITY", "AUTHENTICATION"],
                        value=TemplateState.template_category,
                        on_change=TemplateState.set_template_category,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                columns="3",
                spacing="4",
                width="100%"
            ),

            # WhatsApp Account selection
            rx.vstack(
                rx.text("WhatsApp Account:", weight="bold"),
                rx.select(
                    [f"{acc['account_name']} ({acc['phone_number_id']})" for acc in TemplateState.whatsapp_accounts],
                    placeholder="Select WhatsApp account",
                    on_change=lambda value: TemplateState.set_selected_account(
                        TemplateState.whatsapp_accounts[int(value)]["id"] if value.isdigit() else None
                    ),
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),

            # Template content
            rx.grid(
                rx.vstack(
                    rx.text("Header (optional):", weight="bold"),
                    rx.input(
                        placeholder="Header text",
                        value=TemplateState.template_header_text,
                        on_change=TemplateState.set_template_header,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("Footer (optional):", weight="bold"),
                    rx.input(
                        placeholder="Footer text",
                        value=TemplateState.template_footer_text,
                        on_change=TemplateState.set_template_footer,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                columns="2",
                spacing="4",
                width="100%"
            ),

            rx.vstack(
                rx.text("Body Text:", weight="bold"),
                rx.text_area(
                    placeholder="Your template message body...",
                    value=TemplateState.template_body_text,
                    on_change=TemplateState.set_template_body,
                    height="100px",
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),

            # Action buttons
            rx.hstack(
                rx.button(
                    "Cancel",
                    on_click=TemplateState.toggle_create_form,
                    variant="outline"
                ),
                rx.button(
                    rx.cond(
                        TemplateState.loading,
                        rx.hstack(
                            rx.spinner(size="sm"),
                            "Creating...",
                            spacing="2"
                        ),
                        "Create Template"
                    ),
                    on_click=TemplateState.create_template,
                    disabled=TemplateState.loading,
                    color_scheme="blue"
                ),
                spacing="2"
            ),

            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def templates_page() -> rx.Component:
    """Templates overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Templates",
                "Manage WhatsApp message templates",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "Create Template",
                    on_click=TemplateState.toggle_create_form,
                    color_scheme="blue"
                )
            ),

            # Error/Success messages
            rx.cond(
                TemplateState.error_message != "",
                rx.callout(
                    TemplateState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                TemplateState.success_message != "",
                rx.callout(
                    TemplateState.success_message,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),

            # Template creation form
            rx.cond(
                TemplateState.show_create_form,
                template_creation_form()
            ),

            # Templates list
            rx.vstack(
                rx.hstack(
                    rx.heading("Your Templates", size="lg"),
                    rx.spacer(),
                    rx.button(
                        rx.icon("refresh-cw", size=16),
                        "Refresh",
                        on_click=TemplateState.load_templates,
                        variant="outline",
                        size="sm"
                    ),
                    width="100%",
                    align="center"
                ),

                rx.cond(
                    TemplateState.loading,
                    rx.center(rx.spinner(size="lg"), height="200px"),
                    rx.cond(
                        TemplateState.templates.length() > 0,
                        rx.grid(
                            rx.foreach(
                                TemplateState.templates,
                                template_card
                            ),
                            columns="2",
                            spacing="4",
                            width="100%"
                        ),
                        rx.center(
                            rx.vstack(
                                rx.icon("file-text", size=48, color="gray"),
                                rx.text("No templates found", color="gray"),
                                rx.text("Create your first template to get started", size="sm", color="gray"),
                                spacing="2"
                            ),
                            height="200px"
                        )
                    )
                ),

                spacing="4",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        current_page="templates",
        on_mount=TemplateState.load_templates
    )


def create_template_page() -> rx.Component:
    """Create new template page."""
    return protected_layout(
        rx.vstack(
            page_header("Create Template", "Design a new message template"),
            template_creation_form(),
            spacing="6",
            width="100%"
        ),
        current_page="templates"
    )
