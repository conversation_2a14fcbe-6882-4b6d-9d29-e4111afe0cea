"""Database configuration and session management."""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from contextlib import contextmanager
from typing import Generator
import os

from rxconfig import DATABASE_CONFIG

# Get database URL from environment or config
DATABASE_URL = os.getenv("DATABASE_URL", DATABASE_CONFIG["development"]["url"])

# Create database engine
engine = create_engine(
    DATABASE_URL,
    echo=DATABASE_CONFIG["development"]["echo"],
    pool_pre_ping=True,
    pool_recycle=300,
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@contextmanager
def get_session() -> Generator[Session, None, None]:
    """Get database session with automatic cleanup."""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


def get_db_session() -> Session:
    """Get database session for dependency injection."""
    return SessionLocal()


def init_database():
    """Initialize database tables."""
    from .models.base import BaseModel
    
    # Import all models to ensure they're registered
    from .models import (
        User, UserSession, WhatsAppAccount, WhatsAppChannel,
        MessageTemplate, WhatsAppFlow, Campaign, Message,
        Contact, ContactGroup, BotConfiguration, BotTest,
        BotTestResult, MessageAnalytics, CampaignAnalytics
    )
    
    # Create all tables
    BaseModel.metadata.create_all(bind=engine)
    print("Database tables created successfully!")


def create_sample_data():
    """Create sample data for development."""
    from .models.user import User, UserRole, UserStatus
    from .utils.security import hash_password
    
    with get_session() as session:
        # Check if admin user already exists
        admin_user = session.query(User).filter(User.email == "<EMAIL>").first()
        
        if not admin_user:
            # Create admin user
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=hash_password("admin123"),
                first_name="Admin",
                last_name="User",
                role=UserRole.SUPER_ADMIN,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            session.add(admin_user)
            
            # Create demo user
            demo_user = User(
                email="<EMAIL>",
                username="demo",
                hashed_password=hash_password("demo123"),
                first_name="Demo",
                last_name="User",
                role=UserRole.USER,
                status=UserStatus.ACTIVE,
                is_email_verified=True
            )
            session.add(demo_user)
            
            session.commit()
            print("Sample users created:")
            print("Admin: <EMAIL> / admin123")
            print("Demo: <EMAIL> / demo123")


if __name__ == "__main__":
    # Initialize database when run directly
    init_database()
    create_sample_data()
