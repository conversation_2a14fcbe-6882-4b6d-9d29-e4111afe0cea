{"bot_name": "Advanced Customer Service Bot", "bot_type": "customer_service", "description": "Comprehensive customer service bot for testing various conversation flows and scenarios", "test_scenarios": [{"name": "Welcome Flow", "description": "Test bot welcome and initial responses", "expected_keywords": ["welcome", "hello", "menu", "help", "options"], "messages": [{"text": "hi", "description": "Test welcome trigger", "wait_time": 2}, {"text": "hello", "description": "Test welcome variant", "wait_time": 2}, {"text": "help", "description": "Test help command", "wait_time": 3}]}, {"name": "Menu Navigation", "description": "Test main menu and navigation options", "expected_keywords": ["menu", "option", "select", "choose", "category"], "messages": [{"text": "menu", "description": "Test main menu access", "wait_time": 3}, {"text": "1", "description": "Test menu option selection", "wait_time": 2}, {"text": "back", "description": "Test back navigation", "wait_time": 2}]}, {"name": "Support Request", "description": "Test customer support conversation flow", "expected_keywords": ["support", "agent", "help", "contact", "assistance"], "messages": [{"text": "I need support", "description": "Request support", "wait_time": 3}, {"text": "talk to agent", "description": "Request human agent", "wait_time": 4}, {"text": "urgent issue", "description": "Escalate to urgent support", "wait_time": 3}]}, {"name": "Product Inquiry", "description": "Test product information and pricing queries", "expected_keywords": ["product", "price", "cost", "information", "details"], "messages": [{"text": "product information", "description": "Request product details", "wait_time": 3}, {"text": "pricing", "description": "Ask about pricing", "wait_time": 3}, {"text": "features", "description": "Inquire about features", "wait_time": 2}]}, {"name": "Erro<PERSON>", "description": "Test bot error handling and fallback responses", "expected_keywords": ["sorry", "understand", "help", "try again", "invalid"], "messages": [{"text": "invalid_command_xyz", "description": "Test invalid input", "wait_time": 2}, {"text": "random gibberish text 12345", "description": "Test unrecognized input", "wait_time": 2}, {"text": "reset", "description": "Test bot reset command", "wait_time": 2}]}, {"name": "Business Hours Inquiry", "description": "Test business information queries", "expected_keywords": ["hours", "open", "closed", "schedule", "time"], "messages": [{"text": "what are your hours", "description": "Test hours inquiry", "wait_time": 3}, {"text": "are you open now", "description": "Test current status inquiry", "wait_time": 2}, {"text": "weekend hours", "description": "Test specific schedule inquiry", "wait_time": 3}]}], "expected_responses": {"welcome_keywords": ["welcome", "hello", "menu", "help", "options"], "menu_keywords": ["option", "select", "choose", "menu", "category"], "support_keywords": ["support", "agent", "help", "contact", "assistance"], "product_keywords": ["product", "price", "cost", "information", "details"], "error_keywords": ["sorry", "understand", "help", "try again", "invalid"], "hours_keywords": ["hours", "open", "closed", "schedule", "time"]}, "test_settings": {"default_wait_time": 2, "response_timeout": 10, "scenario_delay": 3, "max_conversation_depth": 20, "retry_failed_messages": true, "log_detailed_responses": true, "test_mode": true, "stop_on_first_failure": false, "parallel_testing": false}}