"""WhatsApp webhook handling service."""

import json
import hmac
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from ..models.whatsapp import Message, WhatsAppAccount
from ..database import get_session

logger = logging.getLogger(__name__)


class WebhookService:
    """Service for handling WhatsApp webhooks."""
    
    def __init__(self, verify_token: str, app_secret: Optional[str] = None):
        """Initialize webhook service."""
        self.verify_token = verify_token
        self.app_secret = app_secret
    
    def verify_webhook(self, mode: str, token: str, challenge: str) -> Optional[str]:
        """Verify webhook subscription."""
        if mode == "subscribe" and token == self.verify_token:
            logger.info("Webhook verified successfully")
            return challenge
        else:
            logger.warning(f"Webhook verification failed: mode={mode}, token={token}")
            return None
    
    def verify_signature(self, payload: str, signature: str) -> bool:
        """Verify webhook payload signature."""
        if not self.app_secret:
            logger.warning("No app secret configured, skipping signature verification")
            return True
        
        try:
            # Remove 'sha256=' prefix if present
            if signature.startswith('sha256='):
                signature = signature[7:]
            
            # Calculate expected signature
            expected_signature = hmac.new(
                self.app_secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            is_valid = hmac.compare_digest(expected_signature, signature)
            
            if not is_valid:
                logger.warning("Webhook signature verification failed")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"Error verifying webhook signature: {str(e)}")
            return False
    
    def process_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming webhook payload."""
        try:
            logger.info(f"Processing webhook payload: {json.dumps(payload, indent=2)}")
            
            # Extract webhook data
            object_type = payload.get("object")
            if object_type != "whatsapp_business_account":
                logger.warning(f"Unsupported webhook object type: {object_type}")
                return {"status": "ignored", "reason": "unsupported_object_type"}
            
            entries = payload.get("entry", [])
            results = []
            
            for entry in entries:
                entry_result = self._process_entry(entry)
                results.append(entry_result)
            
            return {
                "status": "processed",
                "entries_processed": len(results),
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _process_entry(self, entry: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single webhook entry."""
        entry_id = entry.get("id")
        changes = entry.get("changes", [])
        
        results = []
        
        for change in changes:
            change_result = self._process_change(entry_id, change)
            results.append(change_result)
        
        return {
            "entry_id": entry_id,
            "changes_processed": len(results),
            "results": results
        }
    
    def _process_change(self, entry_id: str, change: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single webhook change."""
        field = change.get("field")
        value = change.get("value", {})
        
        if field == "messages":
            return self._process_message_change(entry_id, value)
        elif field == "message_template_status_update":
            return self._process_template_status_change(entry_id, value)
        else:
            logger.info(f"Unhandled webhook field: {field}")
            return {"status": "ignored", "field": field}
    
    def _process_message_change(self, entry_id: str, value: Dict[str, Any]) -> Dict[str, Any]:
        """Process message-related webhook changes."""
        messaging_product = value.get("messaging_product")
        if messaging_product != "whatsapp":
            return {"status": "ignored", "reason": "not_whatsapp"}
        
        metadata = value.get("metadata", {})
        phone_number_id = metadata.get("phone_number_id")
        
        # Process message statuses
        statuses = value.get("statuses", [])
        status_results = []
        
        for status in statuses:
            status_result = self._process_message_status(phone_number_id, status)
            status_results.append(status_result)
        
        # Process incoming messages
        messages = value.get("messages", [])
        message_results = []
        
        for message in messages:
            message_result = self._process_incoming_message(phone_number_id, message)
            message_results.append(message_result)
        
        return {
            "status": "processed",
            "phone_number_id": phone_number_id,
            "statuses_processed": len(status_results),
            "messages_processed": len(message_results),
            "status_results": status_results,
            "message_results": message_results
        }
    
    def _process_message_status(self, phone_number_id: str, status: Dict[str, Any]) -> Dict[str, Any]:
        """Process message status update."""
        try:
            message_id = status.get("id")
            recipient_id = status.get("recipient_id")
            status_type = status.get("status")
            timestamp = status.get("timestamp")
            
            logger.info(f"Processing status update: {message_id} -> {status_type}")
            
            with get_session() as session:
                # Find the message by WhatsApp message ID
                message = session.query(Message).filter(
                    Message.whatsapp_message_id == message_id
                ).first()
                
                if not message:
                    logger.warning(f"Message not found for ID: {message_id}")
                    return {"status": "not_found", "message_id": message_id}
                
                # Update message status
                old_status = message.status
                message.status = status_type
                
                # Update timestamps based on status
                if status_type == "sent" and not message.sent_at:
                    message.sent_at = datetime.fromtimestamp(int(timestamp))
                elif status_type == "delivered" and not message.delivered_at:
                    message.delivered_at = datetime.fromtimestamp(int(timestamp))
                elif status_type == "read" and not message.read_at:
                    message.read_at = datetime.fromtimestamp(int(timestamp))
                elif status_type == "failed":
                    error_info = status.get("errors", [])
                    if error_info:
                        message.error_message = json.dumps(error_info)
                
                # Store webhook data
                message.webhook_data = status
                
                session.commit()
                
                logger.info(f"Updated message {message_id}: {old_status} -> {status_type}")
                
                return {
                    "status": "updated",
                    "message_id": message_id,
                    "old_status": old_status,
                    "new_status": status_type
                }
                
        except Exception as e:
            logger.error(f"Error processing message status: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _process_incoming_message(self, phone_number_id: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming message from user."""
        try:
            message_id = message.get("id")
            from_number = message.get("from")
            message_type = message.get("type")
            timestamp = message.get("timestamp")
            
            logger.info(f"Processing incoming message: {message_id} from {from_number}")
            
            # Extract message content based on type
            content = {}
            if message_type == "text":
                content = message.get("text", {})
            elif message_type == "image":
                content = message.get("image", {})
            elif message_type == "video":
                content = message.get("video", {})
            elif message_type == "audio":
                content = message.get("audio", {})
            elif message_type == "document":
                content = message.get("document", {})
            elif message_type == "location":
                content = message.get("location", {})
            elif message_type == "contacts":
                content = message.get("contacts", {})
            elif message_type == "interactive":
                content = message.get("interactive", {})
            
            with get_session() as session:
                # Find WhatsApp account by phone number ID
                account = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.phone_number_id == phone_number_id
                ).first()
                
                if not account:
                    logger.warning(f"WhatsApp account not found for phone number ID: {phone_number_id}")
                    return {"status": "account_not_found", "phone_number_id": phone_number_id}
                
                # Create incoming message record
                incoming_message = Message(
                    account_id=account.id,
                    whatsapp_message_id=message_id,
                    message_type=message_type,
                    recipient_phone=from_number,  # From user's perspective
                    content=content,
                    status="received",
                    direction="incoming",
                    received_at=datetime.fromtimestamp(int(timestamp)),
                    webhook_data=message
                )
                
                session.add(incoming_message)
                session.commit()
                
                logger.info(f"Stored incoming message: {message_id}")
                
                return {
                    "status": "stored",
                    "message_id": message_id,
                    "message_type": message_type,
                    "from": from_number
                }
                
        except Exception as e:
            logger.error(f"Error processing incoming message: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _process_template_status_change(self, entry_id: str, value: Dict[str, Any]) -> Dict[str, Any]:
        """Process template status change."""
        try:
            template_name = value.get("message_template_name")
            template_language = value.get("message_template_language")
            event = value.get("event")
            
            logger.info(f"Template status change: {template_name} ({template_language}) -> {event}")
            
            with get_session() as session:
                # Find and update template status
                from ..models.whatsapp import MessageTemplate
                
                template = session.query(MessageTemplate).filter(
                    MessageTemplate.template_name == template_name,
                    MessageTemplate.language == template_language
                ).first()
                
                if template:
                    old_status = template.status
                    template.status = event.upper()
                    session.commit()
                    
                    return {
                        "status": "updated",
                        "template_name": template_name,
                        "old_status": old_status,
                        "new_status": event.upper()
                    }
                else:
                    return {
                        "status": "not_found",
                        "template_name": template_name
                    }
                    
        except Exception as e:
            logger.error(f"Error processing template status change: {str(e)}")
            return {"status": "error", "error": str(e)}
