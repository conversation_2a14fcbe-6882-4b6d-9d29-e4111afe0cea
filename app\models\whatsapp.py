"""WhatsApp-related database models."""

import reflex as rx
from sqlmodel import Field
from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey, DateTime, JSON
from enum import Enum as PyEnum
from typing import Optional, List, Dict, Any
from datetime import datetime
from .base import BaseModel


class WhatsAppAccount(BaseModel, table=True):
    """WhatsApp Business Account model."""
    
    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # Account details
    account_name: str = Column(String(255), nullable=False)
    business_account_id: str = Column(String(255), unique=True, nullable=False)
    phone_number_id: str = Column(String(255), nullable=False)
    phone_number: str = Column(String(20), nullable=False)
    
    # API credentials
    access_token: str = Column(Text, nullable=False)
    webhook_verify_token: Optional[str] = Column(String(255), nullable=True)
    
    # Account status
    is_verified: bool = Column(Boolean, default=False)
    is_active: bool = Column(Boolean, default=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class WhatsAppChannel(BaseModel, table=True):
    """WhatsApp Channel configuration."""
    
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Channel details
    channel_name: str = Column(String(255), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class MessageTemplate(BaseModel, table=True):
    """WhatsApp Message Template model."""
    
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Template details
    template_name: str = Column(String(255), nullable=False)
    language: str = Column(String(10), nullable=False)
    category: str = Column(String(50), nullable=False)
    
    # Template content
    header_text: Optional[str] = Column(Text, nullable=True)
    body_text: str = Column(Text, nullable=False)
    footer_text: Optional[str] = Column(Text, nullable=True)
    
    # Template configuration
    template_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Status
    status: str = Column(String(50), default="PENDING")
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class WhatsAppFlow(BaseModel, table=True):
    """WhatsApp Flow model."""
    
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Flow details
    flow_name: str = Column(String(255), nullable=False)
    flow_id: str = Column(String(255), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    
    # Flow configuration
    flow_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Status
    status: str = Column(String(50), default="DRAFT")
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class Campaign(BaseModel, table=True):
    """Marketing Campaign model."""
    
    created_by: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Campaign details
    campaign_name: str = Column(String(255), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    
    # Campaign configuration
    target_audience: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    message_content: Dict[str, Any] = Field(sa_column=Column(JSON, nullable=False))
    
    # Scheduling
    scheduled_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # Status
    status: str = Column(String(50), default="DRAFT")
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class Message(BaseModel, table=True):
    """Individual Message model."""
    
    campaign_id: Optional[int] = Column(Integer, ForeignKey("campaign.id"), nullable=True)
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Message details
    recipient_phone: str = Column(String(20), nullable=False)
    message_content: Dict[str, Any] = Field(sa_column=Column(JSON, nullable=False))
    message_type: str = Column(String(50), nullable=False)
    
    # Status tracking
    status: str = Column(String(50), default="PENDING")
    sent_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    delivered_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    read_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # WhatsApp message ID
    whatsapp_message_id: Optional[str] = Column(String(255), nullable=True)
    
    # Error tracking
    error_message: Optional[str] = Column(Text, nullable=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class Contact(BaseModel, table=True):
    """Contact model."""
    
    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # Contact details
    phone_number: str = Column(String(20), nullable=False)
    first_name: Optional[str] = Column(String(100), nullable=True)
    last_name: Optional[str] = Column(String(100), nullable=True)
    email: Optional[str] = Column(String(255), nullable=True)
    
    # Additional data
    custom_fields: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Status
    is_opted_in: bool = Column(Boolean, default=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class ContactGroup(BaseModel, table=True):
    """Contact Group model."""
    
    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # Group details
    group_name: str = Column(String(255), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    
    # Group configuration
    contact_ids: Optional[List[int]] = Field(default=None, sa_column=Column(JSON))
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
