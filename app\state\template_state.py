"""Template management state."""

import reflex as rx
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from ..models.whatsapp import MessageTemplate, WhatsAppAccount
from ..database import get_session
from .auth_state import AuthState


class TemplateState(AuthState):
    """Template management state."""
    
    # Template list
    templates: List[Dict[str, Any]] = []
    selected_template: Optional[Dict[str, Any]] = None
    
    # Template creation form
    template_name: str = ""
    template_language: str = "en_US"
    template_category: str = "MARKETING"
    template_header_text: str = ""
    template_body_text: str = ""
    template_footer_text: str = ""
    
    # Template parameters
    template_parameters: List[Dict[str, str]] = []
    parameter_name: str = ""
    parameter_example: str = ""
    
    # Template buttons
    template_buttons: List[Dict[str, str]] = []
    button_type: str = "QUICK_REPLY"
    button_text: str = ""
    button_url: str = ""
    button_phone: str = ""
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_create_form: bool = False
    
    # Template preview
    preview_data: Dict[str, Any] = {}
    
    # WhatsApp accounts
    whatsapp_accounts: List[Dict[str, Any]] = []
    selected_account_id: Optional[int] = None
    
    async def load_templates(self):
        """Load user's message templates."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                # Get user's WhatsApp accounts
                accounts = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).all()
                
                self.whatsapp_accounts = [
                    {
                        "id": account.id,
                        "account_name": account.account_name,
                        "phone_number_id": account.phone_number_id,
                        "business_account_id": account.business_account_id
                    }
                    for account in accounts
                ]
                
                # Get templates for all accounts
                account_ids = [account.id for account in accounts]
                if account_ids:
                    templates = session.query(MessageTemplate).filter(
                        MessageTemplate.account_id.in_(account_ids)
                    ).order_by(MessageTemplate.created_at.desc()).all()
                    
                    self.templates = [
                        {
                            "id": template.id,
                            "template_name": template.template_name,
                            "language": template.language,
                            "category": template.category,
                            "header_text": template.header_text,
                            "body_text": template.body_text,
                            "footer_text": template.footer_text,
                            "status": template.status,
                            "account_id": template.account_id,
                            "account_name": next(
                                (acc["account_name"] for acc in self.whatsapp_accounts 
                                 if acc["id"] == template.account_id), 
                                "Unknown"
                            ),
                            "created_at": template.created_at.isoformat() if template.created_at else None,
                            "template_data": template.template_data or {}
                        }
                        for template in templates
                    ]
                
        except Exception as e:
            self.error_message = f"Failed to load templates: {str(e)}"
        finally:
            self.loading = False
    
    def toggle_create_form(self):
        """Toggle template creation form."""
        self.show_create_form = not self.show_create_form
        if self.show_create_form:
            self.clear_form()
        self.clear_messages()
    
    def clear_form(self):
        """Clear template creation form."""
        self.template_name = ""
        self.template_language = "en_US"
        self.template_category = "MARKETING"
        self.template_header_text = ""
        self.template_body_text = ""
        self.template_footer_text = ""
        self.template_parameters = []
        self.template_buttons = []
        self.parameter_name = ""
        self.parameter_example = ""
        self.button_text = ""
        self.button_url = ""
        self.button_phone = ""
        self.preview_data = {}
    
    def add_parameter(self):
        """Add a parameter to the template."""
        if self.parameter_name.strip() and self.parameter_example.strip():
            self.template_parameters.append({
                "name": self.parameter_name.strip(),
                "example": self.parameter_example.strip()
            })
            self.parameter_name = ""
            self.parameter_example = ""
            self.update_preview()
    
    def remove_parameter(self, index: int):
        """Remove a parameter from the template."""
        if 0 <= index < len(self.template_parameters):
            self.template_parameters.pop(index)
            self.update_preview()
    
    def add_button(self):
        """Add a button to the template."""
        if not self.button_text.strip():
            return
        
        button_data = {
            "type": self.button_type,
            "text": self.button_text.strip()
        }
        
        if self.button_type == "URL" and self.button_url.strip():
            button_data["url"] = self.button_url.strip()
        elif self.button_type == "PHONE_NUMBER" and self.button_phone.strip():
            button_data["phone_number"] = self.button_phone.strip()
        
        self.template_buttons.append(button_data)
        self.button_text = ""
        self.button_url = ""
        self.button_phone = ""
        self.update_preview()
    
    def remove_button(self, index: int):
        """Remove a button from the template."""
        if 0 <= index < len(self.template_buttons):
            self.template_buttons.pop(index)
            self.update_preview()
    
    def update_preview(self):
        """Update template preview data."""
        self.preview_data = {
            "name": self.template_name,
            "language": {"code": self.template_language},
            "category": self.template_category,
            "components": []
        }
        
        # Add header component
        if self.template_header_text.strip():
            self.preview_data["components"].append({
                "type": "HEADER",
                "format": "TEXT",
                "text": self.template_header_text
            })
        
        # Add body component
        if self.template_body_text.strip():
            body_component = {
                "type": "BODY",
                "text": self.template_body_text
            }
            
            if self.template_parameters:
                body_component["example"] = {
                    "body_text": [
                        [param["example"] for param in self.template_parameters]
                    ]
                }
            
            self.preview_data["components"].append(body_component)
        
        # Add footer component
        if self.template_footer_text.strip():
            self.preview_data["components"].append({
                "type": "FOOTER",
                "text": self.template_footer_text
            })
        
        # Add buttons component
        if self.template_buttons:
            buttons_component = {
                "type": "BUTTONS",
                "buttons": []
            }
            
            for button in self.template_buttons:
                button_data = {
                    "type": button["type"],
                    "text": button["text"]
                }
                
                if button["type"] == "URL" and "url" in button:
                    button_data["url"] = button["url"]
                elif button["type"] == "PHONE_NUMBER" and "phone_number" in button:
                    button_data["phone_number"] = button["phone_number"]
                
                buttons_component["buttons"].append(button_data)
            
            self.preview_data["components"].append(buttons_component)
    
    async def create_template(self):
        """Create a new message template."""
        if not self.template_name.strip() or not self.template_body_text.strip():
            self.error_message = "Template name and body text are required"
            return
        
        if not self.selected_account_id:
            self.error_message = "Please select a WhatsApp account"
            return
        
        self.loading = True
        try:
            # Update preview data
            self.update_preview()
            
            with get_session() as session:
                new_template = MessageTemplate(
                    account_id=self.selected_account_id,
                    template_name=self.template_name.strip(),
                    language=self.template_language,
                    category=self.template_category,
                    header_text=self.template_header_text.strip() or None,
                    body_text=self.template_body_text.strip(),
                    footer_text=self.template_footer_text.strip() or None,
                    template_data={
                        "parameters": self.template_parameters,
                        "buttons": self.template_buttons,
                        "preview": self.preview_data
                    },
                    status="PENDING"
                )
                
                session.add(new_template)
                session.commit()
                session.refresh(new_template)
                
                self.success_message = f"Template '{self.template_name}' created successfully"
                self.show_create_form = False
                self.clear_form()
                
                # Reload templates
                await self.load_templates()
                
        except Exception as e:
            self.error_message = f"Failed to create template: {str(e)}"
        finally:
            self.loading = False
    
    def select_template(self, template_id: int):
        """Select a template for viewing/editing."""
        self.selected_template = next(
            (template for template in self.templates if template["id"] == template_id),
            None
        )
        self.clear_messages()
    
    async def delete_template(self, template_id: int):
        """Delete a template."""
        self.loading = True
        try:
            with get_session() as session:
                template = session.get(MessageTemplate, template_id)
                if template:
                    session.delete(template)
                    session.commit()
                    
                    self.success_message = "Template deleted successfully"
                    await self.load_templates()
                else:
                    self.error_message = "Template not found"
                    
        except Exception as e:
            self.error_message = f"Failed to delete template: {str(e)}"
        finally:
            self.loading = False
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
    
    def set_template_name(self, name: str):
        """Set template name and update preview."""
        self.template_name = name
        self.update_preview()
    
    def set_template_body(self, body: str):
        """Set template body and update preview."""
        self.template_body_text = body
        self.update_preview()
    
    def set_template_header(self, header: str):
        """Set template header and update preview."""
        self.template_header_text = header
        self.update_preview()
    
    def set_template_footer(self, footer: str):
        """Set template footer and update preview."""
        self.template_footer_text = footer
        self.update_preview()
    
    def set_selected_account(self, account_id: int):
        """Set selected WhatsApp account."""
        self.selected_account_id = account_id
        self.clear_messages()
