"""Bot testing pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.bot_testing_state import BotTestingState


def bot_config_card(config: dict) -> rx.Component:
    """Bot configuration card component."""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(config["bot_name"], size="md"),
                    rx.text(config["bot_type"], color="gray", size="sm"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.button(
                    "Select",
                    on_click=BotTestingState.select_bot_config(config["id"]),
                    size="sm",
                    color_scheme="blue"
                ),
                align="center",
                width="100%"
            ),
            rx.text(
                config["description"] or "No description",
                color="gray",
                size="sm"
            ),
            rx.hstack(
                rx.badge(f"{len(config.get('test_scenarios', []))} scenarios", color_scheme="green"),
                rx.text(f"Created: {config['created_at'][:10] if config['created_at'] else 'Unknown'}", size="xs", color="gray"),
                spacing="2"
            ),
            align="start",
            spacing="3",
            width="100%"
        ),
        width="100%"
    )


def test_result_card(result: dict) -> rx.Component:
    """Test result card component."""
    status_color = {
        "completed": "green",
        "running": "blue",
        "failed": "red",
        "pending": "yellow"
    }.get(result["status"], "gray")

    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(result["test_name"], size="sm"),
                    rx.text(result["config_name"], color="gray", size="xs"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.badge(result["status"], color_scheme=status_color),
                align="center",
                width="100%"
            ),
            rx.hstack(
                rx.text(f"Target: {result['target_phone']}", size="sm"),
                rx.text(f"Type: {result['test_type']}", size="sm", color="gray"),
                spacing="4"
            ),
            rx.text(
                f"Created: {result['created_at'][:16] if result['created_at'] else 'Unknown'}",
                size="xs",
                color="gray"
            ),
            align="start",
            spacing="2",
            width="100%"
        ),
        width="100%"
    )


def bot_testing_page() -> rx.Component:
    """Bot testing overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Bot Testing",
                "Test and validate your WhatsApp bots",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "Upload Config",
                    on_click=BotTestingState.toggle_create_form,
                    color_scheme="blue"
                )
            ),

            # Error/Success messages
            rx.cond(
                BotTestingState.error_message != "",
                rx.callout(
                    BotTestingState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                BotTestingState.success_message != "",
                rx.callout(
                    BotTestingState.success_message,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),

            # Main content
            rx.grid(
                # Left column - Bot configurations
                rx.vstack(
                    rx.hstack(
                        rx.heading("Bot Configurations", size="lg"),
                        rx.spacer(),
                        rx.button(
                            rx.icon("refresh-cw", size=16),
                            "Refresh",
                            on_click=BotTestingState.load_bot_configurations,
                            variant="outline",
                            size="sm"
                        ),
                        width="100%",
                        align="center"
                    ),

                    rx.cond(
                        BotTestingState.loading,
                        rx.center(rx.spinner(size="lg"), height="200px"),
                        rx.cond(
                            BotTestingState.bot_configs.length() > 0,
                            rx.vstack(
                                rx.foreach(
                                    BotTestingState.bot_configs,
                                    bot_config_card
                                ),
                                spacing="3",
                                width="100%"
                            ),
                            rx.center(
                                rx.vstack(
                                    rx.icon("bot", size=48, color="gray"),
                                    rx.text("No bot configurations found", color="gray"),
                                    rx.text("Upload a JSON configuration to get started", size="sm", color="gray"),
                                    spacing="2"
                                ),
                                height="200px"
                            )
                        )
                    ),

                    spacing="4",
                    width="100%"
                ),

                # Right column - Test execution and results
                rx.vstack(
                    rx.heading("Test Execution", size="lg"),

                    # Selected config info
                    rx.cond(
                        BotTestingState.current_bot_config.is_some(),
                        rx.card(
                            rx.vstack(
                                rx.heading("Selected Configuration", size="md"),
                                rx.text(BotTestingState.current_bot_config["bot_name"]),
                                rx.text(BotTestingState.current_bot_config["description"], color="gray", size="sm"),

                                # Test form
                                rx.vstack(
                                    rx.text("Test Phone Number:", weight="bold", size="sm"),
                                    rx.input(
                                        placeholder="+1234567890",
                                        value=BotTestingState.test_phone_number,
                                        on_change=BotTestingState.set_test_phone_number,
                                        width="100%"
                                    ),

                                    rx.button(
                                        rx.cond(
                                            BotTestingState.is_testing,
                                            rx.hstack(
                                                rx.spinner(size="sm"),
                                                "Testing...",
                                                spacing="2"
                                            ),
                                            rx.hstack(
                                                rx.icon("play", size=16),
                                                "Run Smart Test",
                                                spacing="2"
                                            )
                                        ),
                                        on_click=BotTestingState.run_smart_bot_test,
                                        disabled=BotTestingState.is_testing,
                                        color_scheme="green",
                                        width="100%"
                                    ),

                                    spacing="3",
                                    width="100%"
                                ),

                                align="start",
                                spacing="3",
                                width="100%"
                            ),
                            width="100%"
                        ),
                        rx.card(
                            rx.center(
                                rx.vstack(
                                    rx.icon("target", size=32, color="gray"),
                                    rx.text("Select a bot configuration to start testing", color="gray"),
                                    spacing="2"
                                ),
                                height="150px"
                            ),
                            width="100%"
                        )
                    ),

                    spacing="4",
                    width="100%"
                ),

                columns="2",
                spacing="6",
                width="100%"
            ),

            # Test logs and progress
            rx.cond(
                BotTestingState.is_testing | (BotTestingState.test_logs.length() > 0),
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.heading("Test Logs", size="lg"),
                            rx.spacer(),
                            rx.cond(
                                BotTestingState.is_testing,
                                rx.hstack(
                                    rx.text(f"{BotTestingState.test_progress}%", weight="bold"),
                                    rx.progress(value=BotTestingState.test_progress, width="100px"),
                                    spacing="2"
                                )
                            ),
                            width="100%",
                            align="center"
                        ),

                        rx.box(
                            rx.vstack(
                                rx.foreach(
                                    BotTestingState.test_logs,
                                    lambda log: rx.text(log, font_family="mono", size="sm", white_space="pre-wrap")
                                ),
                                spacing="1",
                                width="100%"
                            ),
                            background="black",
                            color="green",
                            padding="4",
                            border_radius="md",
                            max_height="300px",
                            overflow_y="auto",
                            width="100%"
                        ),

                        spacing="4",
                        width="100%"
                    ),
                    width="100%"
                )
            ),

            spacing="6",
            width="100%"
        ),
        current_page="bot-testing",
        on_mount=[BotTestingState.load_bot_configurations, BotTestingState.load_test_results]
    )


def json_config_upload_modal() -> rx.Component:
    """JSON configuration upload modal."""
    return rx.dialog.root(
        rx.dialog.trigger(
            rx.button(
                rx.icon("upload", size=16),
                "Upload JSON Config",
                color_scheme="blue"
            )
        ),
        rx.dialog.content(
            rx.dialog.title("Upload Bot Configuration"),
            rx.dialog.description(
                "Upload a JSON file containing your bot test configuration"
            ),

            rx.vstack(
                # Error/Success messages
                rx.cond(
                    BotTestingState.config_upload_error != "",
                    rx.callout(
                        BotTestingState.config_upload_error,
                        icon="alert-circle",
                        color_scheme="red",
                        width="100%"
                    )
                ),
                rx.cond(
                    BotTestingState.config_upload_success != "",
                    rx.callout(
                        BotTestingState.config_upload_success,
                        icon="check-circle",
                        color_scheme="green",
                        width="100%"
                    )
                ),

                # JSON input
                rx.vstack(
                    rx.text("JSON Configuration:", weight="bold"),
                    rx.text_area(
                        placeholder='{\n  "bot_name": "My Bot",\n  "bot_type": "customer_service",\n  "description": "Bot description",\n  "test_scenarios": [...]\n}',
                        value=BotTestingState.config_json,
                        on_change=BotTestingState.set_config_json,
                        height="300px",
                        width="100%",
                        font_family="mono"
                    ),
                    spacing="2",
                    width="100%"
                ),

                spacing="4",
                width="100%"
            ),

            rx.dialog.close(
                rx.hstack(
                    rx.button(
                        "Cancel",
                        variant="outline"
                    ),
                    rx.button(
                        "Upload Configuration",
                        on_click=BotTestingState.create_bot_config_from_json,
                        color_scheme="blue"
                    ),
                    spacing="2"
                )
            ),

            max_width="600px",
            width="100%"
        )
    )


def create_bot_test_page() -> rx.Component:
    """Create new bot test page."""
    return protected_layout(
        rx.vstack(
            page_header("Create Bot Test", "Set up a new bot test scenario"),

            rx.grid(
                # Upload JSON config
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("upload", size=24, color="blue"),
                            rx.vstack(
                                rx.heading("Upload JSON Configuration", size="md"),
                                rx.text("Import a bot configuration from JSON file", color="gray", size="sm"),
                                align="start",
                                spacing="1"
                            ),
                            align="center",
                            spacing="3"
                        ),

                        json_config_upload_modal(),

                        spacing="4",
                        align="center",
                        width="100%"
                    ),
                    width="100%"
                ),

                # Create from template
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("template", size=24, color="green"),
                            rx.vstack(
                                rx.heading("Use Template", size="md"),
                                rx.text("Start with a pre-built bot configuration", color="gray", size="sm"),
                                align="start",
                                spacing="1"
                            ),
                            align="center",
                            spacing="3"
                        ),

                        rx.button(
                            rx.icon("plus", size=16),
                            "Choose Template",
                            color_scheme="green",
                            disabled=True  # TODO: Implement templates
                        ),

                        spacing="4",
                        align="center",
                        width="100%"
                    ),
                    width="100%"
                ),

                columns="2",
                spacing="6",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        current_page="bot-testing"
    )
