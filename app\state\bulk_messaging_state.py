"""Bulk messaging state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
import csv
import io
import json
import asyncio
from datetime import datetime

from ..models.whatsapp import Message, WhatsAppAccount, Contact, Campaign
from ..database import get_session
from .auth_state import AuthState


class BulkMessagingState(AuthState):
    """Bulk messaging state management."""
    
    # Campaign management
    campaigns: List[Dict[str, Any]] = []
    current_campaign: Optional[Dict[str, Any]] = None
    campaign_name: str = ""
    campaign_description: str = ""
    
    # Recipients management
    recipients: List[Dict[str, str]] = []
    csv_content: str = ""
    csv_upload_error: str = ""
    csv_upload_success: str = ""
    
    # Message configuration
    message_type: str = "text"
    message_content: str = ""
    message_template_name: str = ""
    message_template_params: List[str] = []
    
    # Interactive message components
    interactive_header: str = ""
    interactive_body: str = ""
    interactive_footer: str = ""
    interactive_buttons: List[Dict[str, str]] = []
    
    # Bulk sending configuration
    selected_account_id: Optional[int] = None
    delay_between_messages: float = 1.0
    max_messages_per_minute: int = 30
    test_mode: bool = True
    
    # Sending progress
    is_sending: bool = False
    send_progress: int = 0
    messages_sent: int = 0
    messages_failed: int = 0
    send_logs: List[str] = []
    current_batch_id: Optional[int] = None
    
    # Statistics
    total_recipients: int = 0
    estimated_duration: str = "0 minutes"
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_upload_form: bool = False
    show_message_form: bool = False
    
    # WhatsApp accounts
    whatsapp_accounts: List[Dict[str, Any]] = []
    
    async def load_campaigns(self):
        """Load user's bulk messaging campaigns."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                # Get user's WhatsApp accounts
                accounts = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).all()
                
                self.whatsapp_accounts = [
                    {
                        "id": account.id,
                        "account_name": account.account_name,
                        "phone_number_id": account.phone_number_id,
                        "business_account_id": account.business_account_id
                    }
                    for account in accounts
                ]
                
                # Get campaigns
                account_ids = [account.id for account in accounts]
                if account_ids:
                    campaigns = session.query(Campaign).filter(
                        Campaign.account_id.in_(account_ids)
                    ).order_by(Campaign.created_at.desc()).all()
                    
                    self.campaigns = [
                        {
                            "id": campaign.id,
                            "name": campaign.name,
                            "description": campaign.description,
                            "status": campaign.status,
                            "message_type": campaign.message_type,
                            "total_recipients": campaign.total_recipients,
                            "messages_sent": campaign.messages_sent,
                            "messages_failed": campaign.messages_failed,
                            "created_at": campaign.created_at.isoformat() if campaign.created_at else None,
                            "scheduled_at": campaign.scheduled_at.isoformat() if campaign.scheduled_at else None,
                            "account_name": next(
                                (acc["account_name"] for acc in self.whatsapp_accounts 
                                 if acc["id"] == campaign.account_id), 
                                "Unknown"
                            )
                        }
                        for campaign in campaigns
                    ]
                
        except Exception as e:
            self.error_message = f"Failed to load campaigns: {str(e)}"
        finally:
            self.loading = False
    
    def process_csv_content(self):
        """Process uploaded CSV content and extract recipients."""
        if not self.csv_content.strip():
            self.csv_upload_error = "Please provide CSV content"
            return
        
        try:
            # Parse CSV content
            csv_reader = csv.DictReader(io.StringIO(self.csv_content))
            recipients = []
            
            for row_num, row in enumerate(csv_reader, 1):
                # Look for phone number in various column names
                phone = None
                for key in row.keys():
                    if key.lower() in ['phone', 'phone_number', 'number', 'mobile', 'whatsapp']:
                        phone = row[key].strip()
                        break
                
                if not phone:
                    continue
                
                # Format phone number (basic validation)
                if not phone.startswith('+'):
                    if phone.startswith('1') and len(phone) == 11:
                        phone = '+' + phone
                    elif len(phone) == 10:
                        phone = '+1' + phone
                    else:
                        phone = '+' + phone
                
                recipient = {
                    "phone_number": phone,
                    "name": row.get("name", row.get("Name", "")),
                    "email": row.get("email", row.get("Email", "")),
                    "custom_data": {k: v for k, v in row.items() if k.lower() not in ['phone', 'phone_number', 'number', 'mobile', 'whatsapp', 'name', 'email']}
                }
                recipients.append(recipient)
            
            if not recipients:
                self.csv_upload_error = "No valid phone numbers found in CSV"
                return
            
            self.recipients = recipients
            self.total_recipients = len(recipients)
            self.calculate_estimated_duration()
            
            self.csv_upload_success = f"Successfully loaded {len(recipients)} recipients"
            self.csv_upload_error = ""
            
        except Exception as e:
            self.csv_upload_error = f"Failed to process CSV: {str(e)}"
    
    def add_sample_recipients(self):
        """Add sample recipients for testing."""
        sample_recipients = [
            {"phone_number": "+1234567890", "name": "John Doe", "email": "<EMAIL>", "custom_data": {}},
            {"phone_number": "+1234567891", "name": "Jane Smith", "email": "<EMAIL>", "custom_data": {}},
            {"phone_number": "+1234567892", "name": "Bob Johnson", "email": "<EMAIL>", "custom_data": {}}
        ]
        
        self.recipients = sample_recipients
        self.total_recipients = len(sample_recipients)
        self.calculate_estimated_duration()
        self.csv_upload_success = "Sample recipients added for testing"
    
    def calculate_estimated_duration(self):
        """Calculate estimated sending duration."""
        if self.total_recipients == 0:
            self.estimated_duration = "0 minutes"
            return
        
        # Calculate based on rate limiting
        messages_per_second = self.max_messages_per_minute / 60
        total_seconds = self.total_recipients / messages_per_second
        
        # Add delay between messages
        total_seconds += (self.total_recipients - 1) * self.delay_between_messages
        
        if total_seconds < 60:
            self.estimated_duration = f"{int(total_seconds)} seconds"
        elif total_seconds < 3600:
            self.estimated_duration = f"{int(total_seconds / 60)} minutes"
        else:
            hours = int(total_seconds / 3600)
            minutes = int((total_seconds % 3600) / 60)
            self.estimated_duration = f"{hours}h {minutes}m"
    
    def add_interactive_button(self, button_text: str, button_id: str):
        """Add button to interactive message."""
        if len(self.interactive_buttons) >= 3:
            self.error_message = "Maximum 3 buttons allowed"
            return
        
        if button_text.strip() and button_id.strip():
            self.interactive_buttons.append({
                "id": button_id.strip(),
                "title": button_text.strip()
            })
            self.update_message_preview()
    
    def remove_interactive_button(self, index: int):
        """Remove button from interactive message."""
        if 0 <= index < len(self.interactive_buttons):
            self.interactive_buttons.pop(index)
            self.update_message_preview()
    
    def update_message_preview(self):
        """Update message preview based on current configuration."""
        if self.message_type == "text":
            preview = {
                "type": "text",
                "text": {"body": self.message_content}
            }
        elif self.message_type == "interactive_button":
            preview = {
                "type": "interactive",
                "interactive": {
                    "type": "button",
                    "body": {"text": self.interactive_body},
                    "action": {
                        "buttons": [
                            {
                                "type": "reply",
                                "reply": {
                                    "id": btn["id"],
                                    "title": btn["title"]
                                }
                            }
                            for btn in self.interactive_buttons
                        ]
                    }
                }
            }
            
            if self.interactive_header.strip():
                preview["interactive"]["header"] = {
                    "type": "text",
                    "text": self.interactive_header
                }
            
            if self.interactive_footer.strip():
                preview["interactive"]["footer"] = {
                    "text": self.interactive_footer
                }
        elif self.message_type == "template":
            preview = {
                "type": "template",
                "template": {
                    "name": self.message_template_name,
                    "language": {"code": "en_US"}
                }
            }
            
            if self.message_template_params:
                preview["template"]["components"] = [
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": param}
                            for param in self.message_template_params
                        ]
                    }
                ]
        
        return preview
    
    async def create_campaign(self):
        """Create a new bulk messaging campaign."""
        if not self.campaign_name.strip():
            self.error_message = "Campaign name is required"
            return
        
        if not self.recipients:
            self.error_message = "Please add recipients first"
            return
        
        if not self.selected_account_id:
            self.error_message = "Please select a WhatsApp account"
            return
        
        if not self.message_content.strip() and self.message_type == "text":
            self.error_message = "Message content is required"
            return
        
        self.loading = True
        try:
            message_preview = self.update_message_preview()
            
            with get_session() as session:
                new_campaign = Campaign(
                    account_id=self.selected_account_id,
                    name=self.campaign_name.strip(),
                    description=self.campaign_description.strip(),
                    message_type=self.message_type,
                    message_content=message_preview,
                    total_recipients=len(self.recipients),
                    status="draft",
                    campaign_data={
                        "recipients": self.recipients,
                        "settings": {
                            "delay_between_messages": self.delay_between_messages,
                            "max_messages_per_minute": self.max_messages_per_minute,
                            "test_mode": self.test_mode
                        }
                    }
                )
                
                session.add(new_campaign)
                session.commit()
                session.refresh(new_campaign)
                
                self.current_campaign = {
                    "id": new_campaign.id,
                    "name": new_campaign.name,
                    "description": new_campaign.description,
                    "status": new_campaign.status,
                    "total_recipients": new_campaign.total_recipients
                }
                
                self.success_message = f"Campaign '{self.campaign_name}' created successfully"
                self.campaign_name = ""
                self.campaign_description = ""
                
                # Reload campaigns
                await self.load_campaigns()
                
        except Exception as e:
            self.error_message = f"Failed to create campaign: {str(e)}"
        finally:
            self.loading = False
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
        self.csv_upload_error = ""
        self.csv_upload_success = ""
    
    def toggle_upload_form(self):
        """Toggle CSV upload form."""
        self.show_upload_form = not self.show_upload_form
        self.clear_messages()
    
    def toggle_message_form(self):
        """Toggle message configuration form."""
        self.show_message_form = not self.show_message_form
        self.clear_messages()
    
    def set_csv_content(self, content: str):
        """Set CSV content."""
        self.csv_content = content
        self.csv_upload_error = ""
        self.csv_upload_success = ""
    
    def set_message_content(self, content: str):
        """Set message content."""
        self.message_content = content
        self.clear_messages()
    
    def set_selected_account(self, account_id: int):
        """Set selected WhatsApp account."""
        self.selected_account_id = account_id
        self.clear_messages()
    
    def set_delay_between_messages(self, delay: float):
        """Set delay between messages."""
        self.delay_between_messages = delay
        self.calculate_estimated_duration()
    
    def set_max_messages_per_minute(self, max_msgs: int):
        """Set maximum messages per minute."""
        self.max_messages_per_minute = max_msgs
        self.calculate_estimated_duration()

    def set_campaign_name(self, name: str):
        """Set campaign name."""
        self.campaign_name = name
        self.clear_messages()

    def set_campaign_description(self, description: str):
        """Set campaign description."""
        self.campaign_description = description
        self.clear_messages()

    def set_message_type(self, msg_type: str):
        """Set message type."""
        self.message_type = msg_type
        self.clear_messages()

    def set_message_template_name(self, name: str):
        """Set template name."""
        self.message_template_name = name
        self.clear_messages()

    def set_interactive_header(self, header: str):
        """Set interactive message header."""
        self.interactive_header = header
        self.update_message_preview()

    def set_interactive_body(self, body: str):
        """Set interactive message body."""
        self.interactive_body = body
        self.update_message_preview()

    def set_interactive_footer(self, footer: str):
        """Set interactive message footer."""
        self.interactive_footer = footer
        self.update_message_preview()

    def toggle_test_mode(self):
        """Toggle test mode."""
        self.test_mode = not self.test_mode
        self.clear_messages()
