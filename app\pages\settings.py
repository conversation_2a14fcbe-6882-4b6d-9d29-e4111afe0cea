"""Settings pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header


def settings_page() -> rx.Component:
    """Settings overview page."""
    return protected_layout(
        rx.vstack(
            page_header("Settings", "Manage your account and preferences"),
            rx.text("Settings page - Coming soon!"),
            spacing="6", width="100%"
        ),
        current_page="settings"
    )


def whatsapp_settings_page() -> rx.Component:
    """WhatsApp API settings page."""
    return protected_layout(
        rx.vstack(
            page_header("WhatsApp Settings", "Configure WhatsApp Business API"),
            rx.text("WhatsApp settings page - Coming soon!"),
            spacing="6", width="100%"
        ),
        current_page="settings"
    )
