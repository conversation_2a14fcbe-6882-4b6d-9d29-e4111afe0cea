"""Bulk messaging pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.bulk_messaging_state import BulkMessagingState


def campaign_card(campaign: dict) -> rx.Component:
    """Campaign card component."""
    status_color = {
        "draft": "gray",
        "scheduled": "blue",
        "running": "yellow",
        "completed": "green",
        "failed": "red",
        "paused": "orange"
    }.get(campaign["status"], "gray")
    
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(campaign["name"], size="md"),
                    rx.text(campaign["message_type"], color="gray", size="sm"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.badge(campaign["status"], color_scheme=status_color),
                align="center",
                width="100%"
            ),
            
            rx.text(
                campaign["description"] or "No description",
                color="gray",
                size="sm"
            ),
            
            rx.hstack(
                rx.text(f"Recipients: {campaign['total_recipients']}", size="sm"),
                rx.text(f"Sent: {campaign['messages_sent']}", size="sm", color="green"),
                rx.text(f"Failed: {campaign['messages_failed']}", size="sm", color="red"),
                spacing="4"
            ),
            
            rx.text(
                f"Created: {campaign['created_at'][:10] if campaign['created_at'] else 'Unknown'}",
                size="xs",
                color="gray"
            ),
            
            rx.hstack(
                rx.button(
                    "View",
                    size="sm",
                    variant="outline"
                ),
                rx.button(
                    "Edit",
                    size="sm",
                    variant="outline"
                ),
                rx.button(
                    "Delete",
                    size="sm",
                    color_scheme="red",
                    variant="outline"
                ),
                spacing="2"
            ),
            
            align="start",
            spacing="3",
            width="100%"
        ),
        width="100%"
    )


def recipient_upload_form() -> rx.Component:
    """CSV recipient upload form."""
    return rx.card(
        rx.vstack(
            rx.heading("Upload Recipients", size="lg"),
            
            # Error/Success messages
            rx.cond(
                BulkMessagingState.csv_upload_error != "",
                rx.callout(
                    BulkMessagingState.csv_upload_error,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                BulkMessagingState.csv_upload_success != "",
                rx.callout(
                    BulkMessagingState.csv_upload_success,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),
            
            # CSV upload
            rx.vstack(
                rx.text("CSV Content:", weight="bold"),
                rx.text(
                    "Expected columns: phone_number (or phone), name, email (optional)",
                    size="sm",
                    color="gray"
                ),
                rx.text_area(
                    placeholder="phone_number,name,email\n+1234567890,John Doe,<EMAIL>\n+1234567891,Jane Smith,<EMAIL>",
                    value=BulkMessagingState.csv_content,
                    on_change=BulkMessagingState.set_csv_content,
                    height="200px",
                    width="100%",
                    font_family="mono"
                ),
                spacing="2",
                width="100%"
            ),
            
            # Action buttons
            rx.hstack(
                rx.button(
                    "Add Sample Data",
                    on_click=BulkMessagingState.add_sample_recipients,
                    variant="outline",
                    size="sm"
                ),
                rx.button(
                    "Process CSV",
                    on_click=BulkMessagingState.process_csv_content,
                    color_scheme="blue"
                ),
                spacing="2"
            ),
            
            # Recipients preview
            rx.cond(
                BulkMessagingState.recipients.length() > 0,
                rx.vstack(
                    rx.text(f"Loaded Recipients ({BulkMessagingState.total_recipients}):", weight="bold"),
                    rx.box(
                        rx.vstack(
                            rx.foreach(
                                BulkMessagingState.recipients[:5],  # Show first 5
                                lambda recipient: rx.hstack(
                                    rx.text(recipient["phone_number"], font_family="mono", size="sm"),
                                    rx.text(recipient["name"], size="sm"),
                                    rx.text(recipient["email"], size="sm", color="gray"),
                                    spacing="4"
                                )
                            ),
                            rx.cond(
                                BulkMessagingState.recipients.length() > 5,
                                rx.text(f"... and {BulkMessagingState.recipients.length() - 5} more", size="sm", color="gray")
                            ),
                            spacing="2",
                            width="100%"
                        ),
                        background="gray.50",
                        padding="3",
                        border_radius="md",
                        max_height="200px",
                        overflow_y="auto",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),
            
            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def message_configuration_form() -> rx.Component:
    """Message configuration form."""
    return rx.card(
        rx.vstack(
            rx.heading("Configure Message", size="lg"),
            
            # Message type selector
            rx.vstack(
                rx.text("Message Type:", weight="bold"),
                rx.select(
                    ["text", "interactive_button", "template"],
                    value=BulkMessagingState.message_type,
                    on_change=BulkMessagingState.set_message_type,
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),
            
            # WhatsApp Account selection
            rx.vstack(
                rx.text("WhatsApp Account:", weight="bold"),
                rx.select(
                    [f"{acc['account_name']} ({acc['phone_number_id']})" for acc in BulkMessagingState.whatsapp_accounts],
                    placeholder="Select WhatsApp account",
                    on_change=lambda value: BulkMessagingState.set_selected_account(
                        BulkMessagingState.whatsapp_accounts[int(value)]["id"] if value.isdigit() else None
                    ),
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),
            
            # Text message form
            rx.cond(
                BulkMessagingState.message_type == "text",
                rx.vstack(
                    rx.text("Message Text:", weight="bold"),
                    rx.text_area(
                        placeholder="Enter your bulk message...",
                        value=BulkMessagingState.message_content,
                        on_change=BulkMessagingState.set_message_content,
                        height="100px",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),
            
            # Interactive button message form
            rx.cond(
                BulkMessagingState.message_type == "interactive_button",
                rx.vstack(
                    rx.text("Interactive Button Message:", weight="bold"),
                    
                    rx.grid(
                        rx.input(
                            placeholder="Header (optional)",
                            value=BulkMessagingState.interactive_header,
                            on_change=BulkMessagingState.set_interactive_header,
                            width="100%"
                        ),
                        rx.input(
                            placeholder="Footer (optional)",
                            value=BulkMessagingState.interactive_footer,
                            on_change=BulkMessagingState.set_interactive_footer,
                            width="100%"
                        ),
                        columns="2",
                        spacing="2",
                        width="100%"
                    ),
                    
                    rx.text_area(
                        placeholder="Message body...",
                        value=BulkMessagingState.interactive_body,
                        on_change=BulkMessagingState.set_interactive_body,
                        height="80px",
                        width="100%"
                    ),
                    
                    # Buttons section
                    rx.vstack(
                        rx.text("Buttons (max 3):", weight="bold", size="sm"),
                        rx.hstack(
                            rx.input(
                                placeholder="Button text",
                                width="200px"
                            ),
                            rx.input(
                                placeholder="Button ID",
                                width="150px"
                            ),
                            rx.button(
                                "Add Button",
                                size="sm"
                            ),
                            spacing="2"
                        ),
                        spacing="2",
                        width="100%"
                    ),
                    
                    spacing="3",
                    width="100%"
                )
            ),
            
            # Template message form
            rx.cond(
                BulkMessagingState.message_type == "template",
                rx.vstack(
                    rx.text("Template Message:", weight="bold"),
                    rx.input(
                        placeholder="Template name",
                        value=BulkMessagingState.message_template_name,
                        on_change=BulkMessagingState.set_message_template_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),
            
            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def sending_configuration_form() -> rx.Component:
    """Sending configuration form."""
    return rx.card(
        rx.vstack(
            rx.heading("Sending Configuration", size="lg"),
            
            # Rate limiting settings
            rx.grid(
                rx.vstack(
                    rx.text("Delay Between Messages (seconds):", weight="bold", size="sm"),
                    rx.number_input(
                        value=str(BulkMessagingState.delay_between_messages),
                        on_change=lambda val: BulkMessagingState.set_delay_between_messages(float(val) if val else 1.0),
                        min=0.1,
                        max=60.0,
                        step=0.1,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),
                
                rx.vstack(
                    rx.text("Max Messages Per Minute:", weight="bold", size="sm"),
                    rx.number_input(
                        value=str(BulkMessagingState.max_messages_per_minute),
                        on_change=lambda val: BulkMessagingState.set_max_messages_per_minute(int(val) if val else 30),
                        min=1,
                        max=100,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),
                
                columns="2",
                spacing="4",
                width="100%"
            ),
            
            # Test mode toggle
            rx.hstack(
                rx.switch(
                    checked=BulkMessagingState.test_mode,
                    on_change=BulkMessagingState.toggle_test_mode
                ),
                rx.text("Test Mode (messages won't be actually sent)", size="sm"),
                spacing="2",
                align="center"
            ),
            
            # Statistics
            rx.vstack(
                rx.text("Sending Statistics:", weight="bold"),
                rx.hstack(
                    rx.text(f"Total Recipients: {BulkMessagingState.total_recipients}", size="sm"),
                    rx.text(f"Estimated Duration: {BulkMessagingState.estimated_duration}", size="sm"),
                    spacing="4"
                ),
                spacing="2",
                width="100%"
            ),
            
            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def campaign_creation_form() -> rx.Component:
    """Campaign creation form."""
    return rx.card(
        rx.vstack(
            rx.heading("Create Campaign", size="lg"),

            rx.grid(
                rx.vstack(
                    rx.text("Campaign Name:", weight="bold"),
                    rx.input(
                        placeholder="My Bulk Campaign",
                        value=BulkMessagingState.campaign_name,
                        on_change=BulkMessagingState.set_campaign_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("Description:", weight="bold"),
                    rx.input(
                        placeholder="Campaign description",
                        value=BulkMessagingState.campaign_description,
                        on_change=BulkMessagingState.set_campaign_description,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                columns="2",
                spacing="4",
                width="100%"
            ),

            rx.button(
                rx.cond(
                    BulkMessagingState.loading,
                    rx.hstack(
                        rx.spinner(size="sm"),
                        "Creating Campaign...",
                        spacing="2"
                    ),
                    "Create Campaign"
                ),
                on_click=BulkMessagingState.create_campaign,
                disabled=BulkMessagingState.loading,
                color_scheme="blue",
                width="100%"
            ),

            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def bulk_messaging_page() -> rx.Component:
    """Bulk messaging overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Bulk Messaging",
                "Send messages to multiple recipients",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "New Campaign",
                    on_click=BulkMessagingState.toggle_upload_form,
                    color_scheme="blue"
                )
            ),

            # Error/Success messages
            rx.cond(
                BulkMessagingState.error_message != "",
                rx.callout(
                    BulkMessagingState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                BulkMessagingState.success_message != "",
                rx.callout(
                    BulkMessagingState.success_message,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),

            spacing="6",
            width="100%"
        ),
        current_page="bulk-messaging",
        on_mount=BulkMessagingState.load_campaigns
    )
