"""Reflex configuration for WhatsApp Business API Management Platform."""

import reflex as rx
from typing import Dict, Any

# App configuration
config = rx.Config(
    app_name="app",
    db_url="sqlite:///whatsapp_platform.db",  # Change to PostgreSQL in production
    env=rx.Env.DEV,
    frontend_packages=[
        "react-icons",
        "recharts",
        "react-dropzone",
        "react-beautiful-dnd",
        "@emotion/react",
        "@emotion/styled",
        "framer-motion",
        "react-hook-form",
        "react-query",
        "date-fns",
    ],
    tailwind={
        "theme": {
            "extend": {
                "colors": {
                    "whatsapp": {
                        "50": "#f0fdf4",
                        "100": "#dcfce7", 
                        "200": "#bbf7d0",
                        "300": "#86efac",
                        "400": "#4ade80",
                        "500": "#22c55e",
                        "600": "#16a34a",
                        "700": "#15803d",
                        "800": "#166534",
                        "900": "#14532d",
                    },
                    "primary": {
                        "50": "#eff6ff",
                        "100": "#dbeafe",
                        "200": "#bfdbfe", 
                        "300": "#93c5fd",
                        "400": "#60a5fa",
                        "500": "#3b82f6",
                        "600": "#2563eb",
                        "700": "#1d4ed8",
                        "800": "#1e40af",
                        "900": "#1e3a8a",
                    }
                },
                "fontFamily": {
                    "sans": ["Inter", "system-ui", "sans-serif"],
                    "mono": ["JetBrains Mono", "monospace"],
                },
                "animation": {
                    "fade-in": "fadeIn 0.5s ease-in-out",
                    "slide-up": "slideUp 0.3s ease-out",
                    "pulse-slow": "pulse 3s infinite",
                }
            }
        }
    }
)

# Database configuration
DATABASE_CONFIG: Dict[str, Any] = {
    "development": {
        "url": "sqlite:///whatsapp_platform_dev.db",
        "echo": True,
    },
    "production": {
        "url": "postgresql://user:password@localhost/whatsapp_platform",
        "echo": False,
    }
}

# WhatsApp Business API configuration
WHATSAPP_CONFIG: Dict[str, Any] = {
    "api_version": "v21.0",
    "base_url": "https://graph.facebook.com",
    "webhook_verify_token": "your_webhook_verify_token",
    "rate_limits": {
        "messages_per_second": 10,
        "messages_per_minute": 600,
        "messages_per_hour": 36000,
    }
}

# Security configuration
SECURITY_CONFIG: Dict[str, Any] = {
    "secret_key": "your-super-secret-key-change-in-production",
    "algorithm": "HS256",
    "access_token_expire_minutes": 30,
    "refresh_token_expire_days": 7,
    "password_min_length": 8,
}

# Feature flags
FEATURES: Dict[str, bool] = {
    "enable_flows": True,
    "enable_templates": True,
    "enable_analytics": True,
    "enable_team_collaboration": True,
    "enable_webhook_management": True,
    "enable_campaign_scheduling": True,
    "enable_bot_testing": True,
    "enable_bulk_messaging": True,
}

# Application settings
APP_SETTINGS: Dict[str, Any] = {
    "app_name": "WhatsApp Business Platform",
    "app_description": "Professional WhatsApp Business API Management Platform",
    "version": "2.0.0",
    "contact_email": "<EMAIL>",
    "max_file_size": 16 * 1024 * 1024,  # 16MB
    "supported_file_types": [
        "image/jpeg", "image/png", "image/gif", "image/webp",
        "video/mp4", "video/quicktime",
        "audio/mpeg", "audio/wav", "audio/ogg",
        "application/pdf", "text/plain",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
    ]
}
