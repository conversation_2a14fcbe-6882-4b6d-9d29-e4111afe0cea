"""Admin pages."""

import reflex as rx
from ..components.layout import admin_layout, page_header


def admin_dashboard_page() -> rx.Component:
    """Admin dashboard page."""
    return admin_layout(
        rx.vstack(
            page_header("Admin Dashboard", "System administration and management"),
            rx.text("Admin dashboard - Coming soon!"),
            spacing="6", width="100%"
        ),
        current_page="admin"
    )


def user_management_page() -> rx.Component:
    """User management page."""
    return admin_layout(
        rx.vstack(
            page_header("User Management", "Manage system users and permissions"),
            rx.text("User management page - Coming soon!"),
            spacing="6", width="100%"
        ),
        current_page="admin-users"
    )
