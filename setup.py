"""Setup script for WhatsApp Business Platform."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="whatsapp-business-platform",
    version="2.0.0",
    author="WhatsApp Business Platform Team",
    author_email="<EMAIL>",
    description="Professional WhatsApp Business API Management Platform",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/whatsapp-business-platform",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=[
        "reflex>=0.4.0",
        "sqlalchemy>=2.0.0",
        "alembic>=1.12.0",
        "bcrypt>=4.0.0",
        "python-jose[cryptography]>=3.3.0",
        "passlib[bcrypt]>=1.7.4",
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.4.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.7.0",
            "isort>=5.12.0",
            "mypy>=1.5.0",
        ],
        "production": [
            "psycopg2-binary>=2.9.0",
            "redis>=5.0.0",
            "celery>=5.3.0",
            "gunicorn>=21.2.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "whatsapp-platform=main:main",
        ],
    },
)
