"""WhatsApp webhook API endpoints."""

import reflex as rx
import json
from typing import Dict, Any
import logging

from ..services.webhook_service import WebhookService
from ..database import get_session

logger = logging.getLogger(__name__)

# Initialize webhook service
# In production, these should come from environment variables
WEBHOOK_VERIFY_TOKEN = "your_webhook_verify_token_here"
WEBHOOK_APP_SECRET = "your_app_secret_here"

webhook_service = WebhookService(WEBHOOK_VERIFY_TOKEN, WEBHOOK_APP_SECRET)


class WebhookAPI:
    """WhatsApp webhook API endpoints."""
    
    @staticmethod
    def verify_webhook(hub_mode: str, hub_verify_token: str, hub_challenge: str) -> rx.Response:
        """Verify webhook subscription (GET request)."""
        try:
            logger.info(f"Webhook verification request: mode={hub_mode}, token={hub_verify_token}")
            
            challenge = webhook_service.verify_webhook(hub_mode, hub_verify_token, hub_challenge)
            
            if challenge:
                return rx.Response(
                    content=challenge,
                    status_code=200,
                    headers={"Content-Type": "text/plain"}
                )
            else:
                return rx.Response(
                    content="Forbidden",
                    status_code=403,
                    headers={"Content-Type": "text/plain"}
                )
                
        except Exception as e:
            logger.error(f"Error in webhook verification: {str(e)}")
            return rx.Response(
                content="Internal Server Error",
                status_code=500,
                headers={"Content-Type": "text/plain"}
            )
    
    @staticmethod
    def handle_webhook(request_body: str, signature: str = "") -> rx.Response:
        """Handle incoming webhook (POST request)."""
        try:
            logger.info("Received webhook payload")
            
            # Verify signature if provided
            if signature and not webhook_service.verify_signature(request_body, signature):
                logger.warning("Webhook signature verification failed")
                return rx.Response(
                    content=json.dumps({"error": "Invalid signature"}),
                    status_code=403,
                    headers={"Content-Type": "application/json"}
                )
            
            # Parse JSON payload
            try:
                payload = json.loads(request_body)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON payload: {str(e)}")
                return rx.Response(
                    content=json.dumps({"error": "Invalid JSON"}),
                    status_code=400,
                    headers={"Content-Type": "application/json"}
                )
            
            # Process webhook
            result = webhook_service.process_webhook(payload)
            
            # Return success response
            return rx.Response(
                content=json.dumps(result),
                status_code=200,
                headers={"Content-Type": "application/json"}
            )
            
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}")
            return rx.Response(
                content=json.dumps({"error": "Internal server error"}),
                status_code=500,
                headers={"Content-Type": "application/json"}
            )


def webhook_verify_page() -> rx.Component:
    """Webhook verification endpoint page."""
    # This would typically be handled by a proper API framework
    # For Reflex, we'll create a simple page that explains the webhook setup
    return rx.vstack(
        rx.heading("WhatsApp Webhook Endpoint", size="xl"),
        rx.text("This endpoint handles WhatsApp webhook verification and events."),
        rx.divider(),
        rx.vstack(
            rx.heading("Setup Instructions", size="lg"),
            rx.ordered_list(
                rx.list_item("Configure your webhook URL in Meta Developer Console"),
                rx.list_item("Set the verify token in your environment variables"),
                rx.list_item("Add your app secret for signature verification"),
                rx.list_item("Subscribe to message events in the webhook configuration")
            ),
            align="start",
            spacing="2"
        ),
        rx.divider(),
        rx.vstack(
            rx.heading("Webhook URL", size="lg"),
            rx.code_block(
                "https://yourdomain.com/api/webhooks/whatsapp",
                language="text",
                width="100%"
            ),
            align="start",
            spacing="2"
        ),
        rx.divider(),
        rx.vstack(
            rx.heading("Supported Events", size="lg"),
            rx.unordered_list(
                rx.list_item("Message status updates (sent, delivered, read, failed)"),
                rx.list_item("Incoming messages from users"),
                rx.list_item("Template status changes"),
                rx.list_item("Account updates")
            ),
            align="start",
            spacing="2"
        ),
        spacing="6",
        padding="6",
        max_width="800px",
        margin="0 auto"
    )


def webhook_status_page() -> rx.Component:
    """Webhook status and monitoring page."""
    return rx.vstack(
        rx.heading("Webhook Status", size="xl"),
        rx.text("Monitor your WhatsApp webhook health and recent events."),
        rx.divider(),
        
        # Status indicators
        rx.grid(
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("check-circle", size=24, color="green"),
                        rx.text("Webhook Active", weight="bold"),
                        spacing="2",
                        align="center"
                    ),
                    rx.text("Last event: 2 minutes ago", size="sm", color="gray"),
                    spacing="2",
                    align="start"
                ),
                width="100%"
            ),
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("activity", size=24, color="blue"),
                        rx.text("Events Today", weight="bold"),
                        spacing="2",
                        align="center"
                    ),
                    rx.text("1,247 events processed", size="sm", color="gray"),
                    spacing="2",
                    align="start"
                ),
                width="100%"
            ),
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("clock", size=24, color="orange"),
                        rx.text("Avg Response Time", weight="bold"),
                        spacing="2",
                        align="center"
                    ),
                    rx.text("45ms", size="sm", color="gray"),
                    spacing="2",
                    align="start"
                ),
                width="100%"
            ),
            columns="3",
            spacing="4",
            width="100%"
        ),
        
        rx.divider(),
        
        # Recent events
        rx.vstack(
            rx.heading("Recent Events", size="lg"),
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("message-circle", size=16, color="blue"),
                        rx.text("Message Status Update", weight="bold", size="sm"),
                        rx.spacer(),
                        rx.text("2 min ago", size="xs", color="gray"),
                        width="100%",
                        align="center"
                    ),
                    rx.text("Message delivered to +1234567890", size="sm", color="gray"),
                    spacing="2",
                    align="start",
                    width="100%"
                ),
                width="100%"
            ),
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("user", size=16, color="green"),
                        rx.text("Incoming Message", weight="bold", size="sm"),
                        rx.spacer(),
                        rx.text("5 min ago", size="xs", color="gray"),
                        width="100%",
                        align="center"
                    ),
                    rx.text("User replied: 'Thank you!'", size="sm", color="gray"),
                    spacing="2",
                    align="start",
                    width="100%"
                ),
                width="100%"
            ),
            rx.card(
                rx.vstack(
                    rx.hstack(
                        rx.icon("file-text", size=16, color="purple"),
                        rx.text("Template Status", weight="bold", size="sm"),
                        rx.spacer(),
                        rx.text("1 hour ago", size="xs", color="gray"),
                        width="100%",
                        align="center"
                    ),
                    rx.text("Template 'welcome_message' approved", size="sm", color="gray"),
                    spacing="2",
                    align="start",
                    width="100%"
                ),
                width="100%"
            ),
            spacing="3",
            width="100%"
        ),
        
        spacing="6",
        padding="6",
        max_width="800px",
        margin="0 auto"
    )


# Webhook endpoint handlers (these would be integrated with a proper API framework)
def create_webhook_handlers():
    """Create webhook endpoint handlers."""
    # This is a placeholder for webhook endpoint creation
    # In a real implementation, you would use FastAPI or similar
    webhook_handlers = {
        "verify": WebhookAPI.verify_webhook,
        "handle": WebhookAPI.handle_webhook
    }
    return webhook_handlers
