"""User authentication and management models."""

import reflex as rx
from sqlalchemy import Column, String, Boolean, Enum, Text, ForeignKey, DateTime, Integer
from enum import Enum as PyEnum
from datetime import datetime
from typing import Optional, List
from .base import BaseModel


class UserRole(PyEnum):
    """User roles in the system."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"
    VIEWER = "viewer"


class UserStatus(PyEnum):
    """User account status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING_VERIFICATION = "pending_verification"


class User(BaseModel, table=True):
    """User model for authentication and authorization."""

    # Basic information
    email: str = Column(String(255), unique=True, index=True, nullable=False)
    username: str = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password: str = Column(String(255), nullable=False)
    
    # Profile information
    first_name: str = Column(String(100), nullable=False)
    last_name: str = Column(String(100), nullable=False)
    phone_number: Optional[str] = Column(String(20), nullable=True)
    avatar_url: Optional[str] = Column(String(500), nullable=True)
    bio: Optional[str] = Column(Text, nullable=True)
    
    # Account settings
    role: UserRole = Column(Enum(UserRole), default=UserRole.USER)
    status: UserStatus = Column(Enum(UserStatus), default=UserStatus.PENDING_VERIFICATION)
    is_email_verified: bool = Column(Boolean, default=False)
    is_phone_verified: bool = Column(Boolean, default=False)
    
    # Security settings
    two_factor_enabled: bool = Column(Boolean, default=False)
    two_factor_secret: Optional[str] = Column(String(255), nullable=True)
    last_login: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts: int = Column(Integer, default=0)
    locked_until: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # Preferences
    timezone: str = Column(String(50), default="UTC")
    language: str = Column(String(10), default="en")
    theme: str = Column(String(20), default="light")
    
    # Email preferences
    email_notifications: bool = Column(Boolean, default=True)
    marketing_emails: bool = Column(Boolean, default=False)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN]
    
    @property
    def is_manager(self) -> bool:
        """Check if user has manager privileges."""
        return self.role in [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER]
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def can_access_feature(self, feature: str) -> bool:
        """Check if user can access a specific feature."""
        feature_permissions = {
            "admin_panel": [UserRole.SUPER_ADMIN, UserRole.ADMIN],
            "user_management": [UserRole.SUPER_ADMIN, UserRole.ADMIN],
            "campaign_management": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER],
            "bot_testing": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
            "analytics": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
            "bulk_messaging": [UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.MANAGER, UserRole.USER],
        }
        
        allowed_roles = feature_permissions.get(feature, [])
        return self.role in allowed_roles


class UserSession(BaseModel, table=True):
    """User session tracking for security and analytics."""

    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    session_token: str = Column(String(255), unique=True, index=True, nullable=False)
    refresh_token: str = Column(String(255), unique=True, index=True, nullable=False)
    
    # Session metadata
    ip_address: Optional[str] = Column(String(45), nullable=True)  # IPv6 support
    user_agent: Optional[str] = Column(Text, nullable=True)
    device_type: Optional[str] = Column(String(50), nullable=True)
    browser: Optional[str] = Column(String(100), nullable=True)
    os: Optional[str] = Column(String(100), nullable=True)
    
    # Session timing
    expires_at: datetime = Column(DateTime(timezone=True), nullable=False)
    last_activity: datetime = Column(DateTime(timezone=True), nullable=False)
    
    # Session status
    is_revoked: bool = Column(Boolean, default=False)
    revoked_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    revoked_reason: Optional[str] = Column(String(255), nullable=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if session is valid (not expired and not revoked)."""
        return not self.is_expired and not self.is_revoked
    
    def revoke(self, reason: str = "Manual revocation") -> None:
        """Revoke the session."""
        self.is_revoked = True
        self.revoked_at = datetime.utcnow()
        self.revoked_reason = reason
