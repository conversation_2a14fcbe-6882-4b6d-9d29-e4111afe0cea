"""Simple dashboard page for testing."""

import reflex as rx
from ..components.layout import protected_layout


def simple_dashboard_page() -> rx.Component:
    """Simple dashboard page."""
    return protected_layout(
        rx.vstack(
            rx.heading("WhatsApp Business Platform", size="8"),
            rx.text("Welcome to your dashboard!"),
            
            rx.grid(
                rx.card(
                    rx.vstack(
                        rx.heading("Bot Testing", size="5"),
                        rx.text("Test your WhatsApp bots with JSON configurations"),
                        rx.button(
                            "Go to Bot Testing",
                            on_click=lambda: rx.redirect("/bot-testing"),
                            color_scheme="blue"
                        ),
                        spacing="3",
                        align="start"
                    ),
                    padding="6"
                ),
                
                rx.card(
                    rx.vstack(
                        rx.heading("Bulk Messaging", size="5"),
                        rx.text("Send messages to multiple recipients"),
                        rx.button(
                            "Go to Bulk Messaging",
                            on_click=lambda: rx.redirect("/bulk-messaging"),
                            color_scheme="green"
                        ),
                        spacing="3",
                        align="start"
                    ),
                    padding="6"
                ),
                
                rx.card(
                    rx.vstack(
                        rx.heading("Settings", size="5"),
                        rx.text("Configure your WhatsApp accounts"),
                        rx.button(
                            "Go to Settings",
                            on_click=lambda: rx.redirect("/settings"),
                            color_scheme="purple"
                        ),
                        spacing="3",
                        align="start"
                    ),
                    padding="6"
                ),
                
                columns="3",
                spacing="6",
                width="100%"
            ),
            
            spacing="6",
            width="100%"
        ),
        current_page="dashboard"
    )
