"""Bot testing related models."""

import reflex as rx
from sqlmodel import Field
from sqlalchemy import Column, String, Text, Integer, Boolean, ForeignKey, DateTime, JSON, Float
from typing import Optional, Dict, Any
from datetime import datetime
from .base import BaseModel


class BotConfiguration(BaseModel, table=True):
    """Bot test configuration model."""
    
    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # Bot details
    bot_name: str = Column(String(255), nullable=False)
    bot_type: str = Column(String(100), nullable=False)
    description: Optional[str] = Column(Text, nullable=True)
    
    # Configuration data
    test_scenarios: Dict[str, Any] = Field(sa_column=Column(JSON, nullable=False))
    expected_responses: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    test_settings: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Status
    is_active: bool = Column(Boolean, default=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class BotTest(BaseModel, table=True):
    """Bot test execution model."""
    
    configuration_id: int = Column(Integer, ForeignKey("botconfiguration.id"), nullable=False)
    user_id: int = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # Test details
    test_name: str = Column(String(255), nullable=False)
    target_phone: str = Column(String(20), nullable=False)
    
    # Test execution
    started_at: datetime = Column(DateTime(timezone=True), nullable=False)
    completed_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # Test status
    status: str = Column(String(50), default="RUNNING")  # RUNNING, COMPLETED, FAILED
    
    # Test results summary
    total_scenarios: int = Column(Integer, default=0)
    passed_scenarios: int = Column(Integer, default=0)
    failed_scenarios: int = Column(Integer, default=0)
    
    # Performance metrics
    total_messages_sent: int = Column(Integer, default=0)
    average_response_time: Optional[float] = Column(Float, nullable=True)
    
    # Error tracking
    error_message: Optional[str] = Column(Text, nullable=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class BotTestResult(BaseModel, table=True):
    """Individual bot test scenario result."""
    
    test_id: int = Column(Integer, ForeignKey("bottest.id"), nullable=False)
    
    # Scenario details
    scenario_name: str = Column(String(255), nullable=False)
    scenario_order: int = Column(Integer, nullable=False)
    
    # Test execution
    started_at: datetime = Column(DateTime(timezone=True), nullable=False)
    completed_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    
    # Test input/output
    test_message: str = Column(Text, nullable=False)
    expected_response: Optional[str] = Column(Text, nullable=True)
    actual_response: Optional[str] = Column(Text, nullable=True)
    
    # Result
    status: str = Column(String(50), nullable=False)  # PASSED, FAILED, TIMEOUT
    response_time: Optional[float] = Column(Float, nullable=True)
    
    # Additional data
    test_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    error_details: Optional[str] = Column(Text, nullable=True)
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
