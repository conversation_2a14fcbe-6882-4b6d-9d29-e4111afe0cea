"""Navigation components - sidebar and navbar."""

import reflex as rx
from ..state.auth_state import AuthState


def sidebar(current_page: str = "") -> rx.Component:
    """Application sidebar with navigation menu."""
    
    # Navigation items
    nav_items = [
        {"label": "Dashboard", "icon": "home", "href": "/dashboard", "key": "dashboard"},
        {"label": "Messages", "icon": "message-circle", "href": "/messages", "key": "messages"},
        {"label": "Bulk Messaging", "icon": "send", "href": "/bulk-messaging", "key": "bulk-messaging"},
        {"label": "Campaigns", "icon": "megaphone", "href": "/campaigns", "key": "campaigns"},
        {"label": "Templates", "icon": "file-text", "href": "/templates", "key": "templates"},
        {"label": "Flows", "icon": "workflow", "href": "/flows", "key": "flows"},
        {"label": "Contacts", "icon": "users", "href": "/contacts", "key": "contacts"},
        {"label": "Bot Testing", "icon": "bot", "href": "/bot-testing", "key": "bot-testing"},
        {"label": "Analytics", "icon": "bar-chart", "href": "/analytics", "key": "analytics"},
    ]
    
    admin_items = [
        {"label": "Admin Panel", "icon": "shield", "href": "/admin", "key": "admin"},
        {"label": "User Management", "icon": "user-cog", "href": "/admin/users", "key": "admin-users"},
    ]
    
    return rx.box(
        rx.vstack(
            # Logo and brand
            rx.hstack(
                rx.icon("message-circle", size=32, color="blue"),
                rx.vstack(
                    rx.text("WhatsApp", size="5", weight="bold"),
                    rx.text("Business Platform", size="1", color="gray"),
                    spacing="0",
                    align="start"
                ),
                spacing="3",
                align="center",
                padding_x="4",
                padding_y="6"
            ),
            
            rx.divider(),
            
            # Main navigation
            rx.vstack(
                rx.foreach(
                    nav_items,
                    lambda item: nav_item(
                        item["label"],
                        item["icon"], 
                        item["href"],
                        current_page == item["key"]
                    )
                ),
                spacing="1",
                width="100%",
                padding_x="3"
            ),
            
            # Admin section (only for admins)
            rx.cond(
                AuthState.current_user.get("is_admin", False) if AuthState.current_user else False,
                rx.vstack(
                    rx.divider(margin_y="4"),
                    rx.text("Admin", size="1", color="gray", padding_x="4"),
                    rx.vstack(
                        rx.foreach(
                            admin_items,
                            lambda item: nav_item(
                                item["label"],
                                item["icon"],
                                item["href"], 
                                current_page == item["key"]
                            )
                        ),
                        spacing="1",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%",
                    padding_x="3"
                )
            ),
            
            rx.spacer(),
            
            # Settings and logout
            rx.vstack(
                rx.divider(),
                nav_item("Settings", "settings", "/settings", current_page == "settings"),
                rx.button(
                    rx.hstack(
                        rx.icon("log-out", size=16),
                        rx.text("Logout"),
                        spacing="2",
                        align="center"
                    ),
                    on_click=AuthState.logout,
                    variant="ghost",
                    width="100%",
                    justify="start",
                    color_scheme="red"
                ),
                spacing="1",
                width="100%",
                padding_x="3",
                padding_bottom="4"
            ),
            
            spacing="0",
            width="100%",
            height="100vh",
            align="start"
        ),
        width="250px",
        bg="white",
        border_right="1px solid",
        border_color="gray.200",
        position="fixed",
        height="100vh",
        overflow_y="auto"
    )


def nav_item(label: str, icon: str, href: str, is_active: bool = False) -> rx.Component:
    """Individual navigation item."""
    return rx.link(
        rx.hstack(
            rx.icon(icon, size=16),
            rx.text(label, size="2"),
            spacing="3",
            align="center",
            width="100%"
        ),
        href=href,
        width="100%",
        _hover={"text_decoration": "none"},
        style=rx.cond(
            is_active,
            {
                "padding": "8px 12px",
                "border_radius": "6px",
                "background_color": "blue.500",
                "color": "white",
                "_hover": {
                    "background_color": "blue.500"
                }
            },
            {
                "padding": "8px 12px",
                "border_radius": "6px",
                "background_color": "transparent",
                "color": "gray.700",
                "_hover": {
                    "background_color": "gray.100"
                }
            }
        )
    )


def navbar() -> rx.Component:
    """Top navigation bar."""
    return rx.box(
        rx.hstack(
            # Left side - could add breadcrumbs or page title here
            rx.box(),
            
            rx.spacer(),
            
            # Right side - user menu and notifications
            rx.hstack(
                # Notifications
                rx.button(
                    rx.icon("bell", size=16),
                    variant="ghost",
                    size="sm"
                ),
                
                # User menu
                rx.menu.root(
                    rx.menu.trigger(
                        rx.button(
                            rx.hstack(
                                rx.avatar(
                                    fallback=AuthState.current_user.get("first_name", "U")[0] if AuthState.current_user else "U",
                                    size="sm"
                                ),
                                rx.vstack(
                                    rx.text(
                                        AuthState.current_user.get("full_name", "User") if AuthState.current_user else "User",
                                        size="2",
                                        weight="medium"
                                    ),
                                    rx.text(
                                        AuthState.current_user.get("email", "") if AuthState.current_user else "",
                                        size="1",
                                        color="gray"
                                    ),
                                    spacing="0",
                                    align="start"
                                ),
                                rx.icon("chevron-down", size=16),
                                spacing="2",
                                align="center"
                            ),
                            variant="ghost",
                            size="sm"
                        )
                    ),
                    rx.menu.content(
                        rx.menu.item(
                            rx.hstack(
                                rx.icon("user", size=16),
                                rx.text("Profile"),
                                spacing="2"
                            ),
                            on_click=lambda: rx.redirect("/settings")
                        ),
                        rx.menu.item(
                            rx.hstack(
                                rx.icon("settings", size=16),
                                rx.text("Settings"),
                                spacing="2"
                            ),
                            on_click=lambda: rx.redirect("/settings")
                        ),
                        rx.menu.separator(),
                        rx.menu.item(
                            rx.hstack(
                                rx.icon("log-out", size=16),
                                rx.text("Logout"),
                                spacing="2"
                            ),
                            on_click=AuthState.logout,
                            color="red"
                        )
                    )
                ),
                
                spacing="3",
                align="center"
            ),
            
            width="100%",
            justify="between",
            align="center",
            padding_x="6",
            padding_y="3"
        ),
        width="100%",
        bg="white",
        border_bottom="1px solid",
        border_color="gray.200",
        position="sticky",
        top="0",
        z_index="10",
        margin_left="250px"  # Account for sidebar width
    )


def breadcrumbs(items: list) -> rx.Component:
    """Breadcrumb navigation component."""
    return rx.hstack(
        rx.foreach(
            items,
            lambda item, index: rx.hstack(
                rx.cond(
                    index > 0,
                    rx.icon("chevron-right", size=16, color="gray")
                ),
                rx.cond(
                    item.get("href"),
                    rx.link(
                        item["label"],
                        href=item["href"],
                        color="blue",
                        size="2"
                    ),
                    rx.text(
                        item["label"],
                        size="2",
                        color="gray"
                    )
                ),
                spacing="2"
            )
        ),
        spacing="1",
        align="center"
    )


def mobile_nav_toggle() -> rx.Component:
    """Mobile navigation toggle button."""
    return rx.button(
        rx.icon("menu", size=20),
        variant="ghost",
        size="sm",
        display=["block", "block", "none"]  # Show on mobile, hide on desktop
    )
