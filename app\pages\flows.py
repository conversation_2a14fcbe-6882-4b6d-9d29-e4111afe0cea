"""Flow management pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.flow_builder_state import FlowBuilderState


def flow_card(flow: dict) -> rx.Component:
    """Flow card component."""
    status_color = {
        "DRAFT": "gray",
        "PUBLISHED": "green",
        "DEPRECATED": "red",
        "BLOCKED": "red"
    }.get(flow["status"], "gray")

    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.heading(flow["name"], size="md"),
                    rx.text(flow["category"], color="gray", size="sm"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.badge(flow["status"], color_scheme=status_color),
                align="center",
                width="100%"
            ),

            rx.text(
                flow["description"] or "No description",
                color="gray",
                size="sm"
            ),

            rx.text(
                f"Account: {flow['account_name']}",
                size="xs",
                color="gray"
            ),

            rx.hstack(
                rx.button(
                    "Edit",
                    on_click=FlowBuilderState.load_flow_for_editing(flow["id"]),
                    size="sm",
                    variant="outline"
                ),
                rx.button(
                    "Preview",
                    size="sm",
                    variant="outline"
                ),
                rx.button(
                    "Delete",
                    size="sm",
                    color_scheme="red",
                    variant="outline"
                ),
                spacing="2"
            ),

            align="start",
            spacing="3",
            width="100%"
        ),
        width="100%"
    )


def flow_builder_interface() -> rx.Component:
    """Flow builder interface."""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.heading("Flow Builder", size="lg"),
                rx.spacer(),
                rx.button(
                    "Close",
                    on_click=FlowBuilderState.toggle_flow_builder,
                    variant="outline"
                ),
                width="100%",
                align="center"
            ),

            # Flow properties
            rx.grid(
                rx.vstack(
                    rx.text("Flow Name:", weight="bold"),
                    rx.input(
                        placeholder="My Flow",
                        value=FlowBuilderState.flow_name,
                        on_change=FlowBuilderState.set_flow_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("Category:", weight="bold"),
                    rx.select(
                        ["SIGN_UP", "LEAD_GENERATION", "APPOINTMENT_BOOKING", "CUSTOMER_SUPPORT"],
                        value=FlowBuilderState.flow_category,
                        on_change=FlowBuilderState.set_flow_category,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                columns="2",
                spacing="4",
                width="100%"
            ),

            rx.vstack(
                rx.text("Description:", weight="bold"),
                rx.text_area(
                    placeholder="Flow description...",
                    value=FlowBuilderState.flow_description,
                    on_change=FlowBuilderState.set_flow_description,
                    height="80px",
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),

            # Screen management
            rx.divider(),

            rx.vstack(
                rx.heading("Screens", size="md"),

                # Add new screen
                rx.card(
                    rx.vstack(
                        rx.text("Add New Screen:", weight="bold"),
                        rx.grid(
                            rx.input(
                                placeholder="Screen name (e.g., WELCOME)",
                                value=FlowBuilderState.screen_name,
                                on_change=FlowBuilderState.set_screen_name,
                                width="100%"
                            ),
                            rx.input(
                                placeholder="Screen title",
                                value=FlowBuilderState.screen_title,
                                on_change=FlowBuilderState.set_screen_title,
                                width="100%"
                            ),
                            columns="2",
                            spacing="2",
                            width="100%"
                        ),
                        rx.button(
                            "Add Screen",
                            on_click=FlowBuilderState.add_screen,
                            color_scheme="blue",
                            size="sm"
                        ),
                        spacing="3",
                        width="100%"
                    ),
                    width="100%"
                ),

                # Existing screens
                rx.cond(
                    FlowBuilderState.flow_screens.length() > 0,
                    rx.vstack(
                        rx.foreach(
                            FlowBuilderState.flow_screens,
                            lambda screen: rx.card(
                                rx.vstack(
                                    rx.hstack(
                                        rx.text(screen["title"], weight="bold"),
                                        rx.spacer(),
                                        rx.button(
                                            "Edit",
                                            on_click=FlowBuilderState.select_screen(screen["id"]),
                                            size="xs"
                                        ),
                                        width="100%",
                                        align="center"
                                    ),
                                    rx.text(f"ID: {screen['name']}", size="sm", color="gray"),
                                    rx.text(f"Components: {screen['layout']['children'].length()}", size="sm", color="gray"),
                                    spacing="2",
                                    width="100%"
                                ),
                                width="100%"
                            )
                        ),
                        spacing="2",
                        width="100%"
                    )
                ),

                spacing="4",
                width="100%"
            ),

            # Save flow
            rx.button(
                rx.cond(
                    FlowBuilderState.loading,
                    rx.hstack(
                        rx.spinner(size="sm"),
                        "Saving...",
                        spacing="2"
                    ),
                    "Save Flow"
                ),
                on_click=FlowBuilderState.save_flow,
                disabled=FlowBuilderState.loading,
                color_scheme="green",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        width="100%"
    )


def flows_page() -> rx.Component:
    """Flows overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Flows",
                "Manage WhatsApp interactive flows",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "Create Flow",
                    on_click=FlowBuilderState.create_new_flow,
                    color_scheme="blue"
                )
            ),

            # Error/Success messages
            rx.cond(
                FlowBuilderState.error_message != "",
                rx.callout(
                    FlowBuilderState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                FlowBuilderState.success_message != "",
                rx.callout(
                    FlowBuilderState.success_message,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),

            # Flow builder interface
            rx.cond(
                FlowBuilderState.show_flow_builder,
                flow_builder_interface()
            ),

            # Existing flows
            rx.vstack(
                rx.hstack(
                    rx.heading("Your Flows", size="lg"),
                    rx.spacer(),
                    rx.button(
                        rx.icon("refresh-cw", size=16),
                        "Refresh",
                        on_click=FlowBuilderState.load_flows,
                        variant="outline",
                        size="sm"
                    ),
                    width="100%",
                    align="center"
                ),

                rx.cond(
                    FlowBuilderState.loading,
                    rx.center(rx.spinner(size="lg"), height="200px"),
                    rx.cond(
                        FlowBuilderState.flows.length() > 0,
                        rx.grid(
                            rx.foreach(
                                FlowBuilderState.flows,
                                flow_card
                            ),
                            columns="2",
                            spacing="4",
                            width="100%"
                        ),
                        rx.center(
                            rx.vstack(
                                rx.icon("workflow", size=48, color="gray"),
                                rx.text("No flows found", color="gray"),
                                rx.text("Create your first interactive flow", size="sm", color="gray"),
                                spacing="2"
                            ),
                            height="200px"
                        )
                    )
                ),

                spacing="4",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        current_page="flows",
        on_mount=FlowBuilderState.load_flows
    )


def flow_builder_page() -> rx.Component:
    """Flow builder page."""
    return protected_layout(
        rx.vstack(
            page_header("Flow Builder", "Create interactive WhatsApp flows"),
            flow_builder_interface(),
            spacing="6",
            width="100%"
        ),
        current_page="flows"
    )
