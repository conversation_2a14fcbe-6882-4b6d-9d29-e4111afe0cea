"""Message and modern WhatsApp features state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from ..models.whatsapp import Message, WhatsAppAccount, Contact
from ..database import get_session
from ..services.whatsapp_api import WhatsAppAPIService, WhatsAppAPIError
from .auth_state import AuthState


class MessageState(AuthState):
    """Message and modern WhatsApp features state management."""
    
    # Message list
    messages: List[Dict[str, Any]] = []
    selected_message: Optional[Dict[str, Any]] = None
    
    # Message composition
    message_type: str = "text"
    message_text: str = ""
    recipient_phone: str = ""
    
    # Interactive message components
    # Button message
    button_header: str = ""
    button_body: str = ""
    button_footer: str = ""
    button_list: List[Dict[str, str]] = []
    current_button_text: str = ""
    current_button_id: str = ""
    
    # List message
    list_header: str = ""
    list_body: str = ""
    list_footer: str = ""
    list_button_text: str = "Select Option"
    list_sections: List[Dict[str, Any]] = []
    current_section_title: str = ""
    current_section_rows: List[Dict[str, str]] = []
    current_row_title: str = ""
    current_row_description: str = ""
    current_row_id: str = ""
    
    # Flow message
    flow_id: str = ""
    flow_cta: str = "Open Flow"
    flow_mode: str = "published"
    flow_data: Dict[str, Any] = {}
    
    # Template message
    template_name: str = ""
    template_language: str = "en_US"
    template_parameters: List[str] = []
    current_parameter: str = ""
    
    # Location message
    location_latitude: float = 0.0
    location_longitude: float = 0.0
    location_name: str = ""
    location_address: str = ""
    
    # Contact message
    contact_name: str = ""
    contact_phone: str = ""
    contact_email: str = ""
    contact_organization: str = ""
    
    # Media message
    media_type: str = "image"
    media_url: str = ""
    media_caption: str = ""
    
    # Carousel message
    carousel_cards: List[Dict[str, Any]] = []
    current_card_title: str = ""
    current_card_subtitle: str = ""
    current_card_image: str = ""
    current_card_buttons: List[Dict[str, str]] = []

    # Advanced interactive list message
    list_sections_advanced: List[Dict[str, Any]] = []
    current_section_advanced: Dict[str, Any] = {}

    # Flow message (advanced)
    flow_header_text: str = ""
    flow_body_text: str = ""
    flow_footer_text: str = ""
    flow_action_parameters: Dict[str, Any] = {}

    # Product message
    product_catalog_id: str = ""
    product_retailer_id: str = ""
    product_title: str = ""
    product_body: str = ""
    product_footer: str = ""

    # Order message
    order_details: Dict[str, Any] = {}
    order_status: str = "pending"
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_compose_form: bool = False
    
    # Message preview
    preview_data: Dict[str, Any] = {}
    
    # WhatsApp accounts and contacts
    whatsapp_accounts: List[Dict[str, Any]] = []
    contacts: List[Dict[str, Any]] = []
    selected_account_id: Optional[int] = None
    
    async def load_messages(self):
        """Load user's messages."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                # Get user's WhatsApp accounts
                accounts = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).all()
                
                self.whatsapp_accounts = [
                    {
                        "id": account.id,
                        "account_name": account.account_name,
                        "phone_number_id": account.phone_number_id
                    }
                    for account in accounts
                ]
                
                # Get messages for all accounts
                account_ids = [account.id for account in accounts]
                if account_ids:
                    messages = session.query(Message).filter(
                        Message.account_id.in_(account_ids)
                    ).order_by(Message.created_at.desc()).limit(50).all()
                    
                    self.messages = [
                        {
                            "id": message.id,
                            "message_type": message.message_type,
                            "recipient_phone": message.recipient_phone,
                            "content": message.content,
                            "status": message.status,
                            "account_id": message.account_id,
                            "created_at": message.created_at.isoformat() if message.created_at else None,
                            "sent_at": message.sent_at.isoformat() if message.sent_at else None,
                            "delivered_at": message.delivered_at.isoformat() if message.delivered_at else None,
                            "read_at": message.read_at.isoformat() if message.read_at else None
                        }
                        for message in messages
                    ]
                
                # Load contacts
                contacts = session.query(Contact).filter(
                    Contact.account_id.in_(account_ids)
                ).order_by(Contact.name).all()
                
                self.contacts = [
                    {
                        "id": contact.id,
                        "name": contact.name,
                        "phone_number": contact.phone_number,
                        "email": contact.email
                    }
                    for contact in contacts
                ]
                
        except Exception as e:
            self.error_message = f"Failed to load messages: {str(e)}"
        finally:
            self.loading = False
    
    def toggle_compose_form(self):
        """Toggle message composition form."""
        self.show_compose_form = not self.show_compose_form
        if self.show_compose_form:
            self.clear_form()
        self.clear_messages()
    
    def clear_form(self):
        """Clear message composition form."""
        self.message_text = ""
        self.recipient_phone = ""
        self.button_header = ""
        self.button_body = ""
        self.button_footer = ""
        self.button_list = []
        self.list_header = ""
        self.list_body = ""
        self.list_footer = ""
        self.list_sections = []
        self.flow_id = ""
        self.flow_cta = "Open Flow"
        self.template_name = ""
        self.template_parameters = []
        self.location_latitude = 0.0
        self.location_longitude = 0.0
        self.location_name = ""
        self.location_address = ""
        self.contact_name = ""
        self.contact_phone = ""
        self.contact_email = ""
        self.media_url = ""
        self.media_caption = ""
        self.carousel_cards = []
        self.preview_data = {}
    
    def add_button(self):
        """Add button to interactive message."""
        if self.current_button_text.strip() and self.current_button_id.strip():
            self.button_list.append({
                "id": self.current_button_id.strip(),
                "title": self.current_button_text.strip()
            })
            self.current_button_text = ""
            self.current_button_id = ""
            self.update_preview()
    
    def remove_button(self, index: int):
        """Remove button from interactive message."""
        if 0 <= index < len(self.button_list):
            self.button_list.pop(index)
            self.update_preview()
    
    def add_list_row(self):
        """Add row to current list section."""
        if self.current_row_title.strip() and self.current_row_id.strip():
            self.current_section_rows.append({
                "id": self.current_row_id.strip(),
                "title": self.current_row_title.strip(),
                "description": self.current_row_description.strip()
            })
            self.current_row_title = ""
            self.current_row_description = ""
            self.current_row_id = ""
    
    def add_list_section(self):
        """Add section to list message."""
        if self.current_section_title.strip() and self.current_section_rows:
            self.list_sections.append({
                "title": self.current_section_title.strip(),
                "rows": self.current_section_rows.copy()
            })
            self.current_section_title = ""
            self.current_section_rows = []
            self.update_preview()
    
    def add_template_parameter(self):
        """Add parameter to template message."""
        if self.current_parameter.strip():
            self.template_parameters.append(self.current_parameter.strip())
            self.current_parameter = ""
            self.update_preview()
    
    def remove_template_parameter(self, index: int):
        """Remove parameter from template message."""
        if 0 <= index < len(self.template_parameters):
            self.template_parameters.pop(index)
            self.update_preview()

    def add_carousel_card(self):
        """Add card to carousel message."""
        if self.current_card_title.strip():
            card = {
                "title": self.current_card_title.strip(),
                "subtitle": self.current_card_subtitle.strip(),
                "image_url": self.current_card_image.strip(),
                "buttons": self.current_card_buttons.copy()
            }
            self.carousel_cards.append(card)

            # Clear current card fields
            self.current_card_title = ""
            self.current_card_subtitle = ""
            self.current_card_image = ""
            self.current_card_buttons = []

            self.update_preview()

    def remove_carousel_card(self, index: int):
        """Remove card from carousel message."""
        if 0 <= index < len(self.carousel_cards):
            self.carousel_cards.pop(index)
            self.update_preview()

    def add_card_button(self, button_text: str, button_type: str = "postback", button_payload: str = ""):
        """Add button to current carousel card."""
        if len(self.current_card_buttons) >= 3:
            return  # Max 3 buttons per card

        if button_text.strip():
            button = {
                "type": button_type,
                "title": button_text.strip()
            }

            if button_type == "postback":
                button["payload"] = button_payload or button_text.strip().lower().replace(" ", "_")
            elif button_type == "web_url":
                button["url"] = button_payload
            elif button_type == "phone_number":
                button["phone_number"] = button_payload

            self.current_card_buttons.append(button)

    def create_advanced_list_section(self, title: str, rows: List[Dict[str, str]]):
        """Create an advanced list section with multiple rows."""
        if title.strip() and rows:
            section = {
                "title": title.strip(),
                "rows": [
                    {
                        "id": row.get("id", f"row_{i}"),
                        "title": row.get("title", ""),
                        "description": row.get("description", "")
                    }
                    for i, row in enumerate(rows)
                ]
            }
            self.list_sections_advanced.append(section)
            self.update_preview()

    def create_product_message(self):
        """Create a product catalog message."""
        if not self.product_catalog_id or not self.product_retailer_id:
            return

        product_message = {
            "type": "interactive",
            "interactive": {
                "type": "product",
                "body": {"text": self.product_body},
                "action": {
                    "catalog_id": self.product_catalog_id,
                    "product_retailer_id": self.product_retailer_id
                }
            }
        }

        if self.product_title.strip():
            product_message["interactive"]["header"] = {
                "type": "text",
                "text": self.product_title
            }

        if self.product_footer.strip():
            product_message["interactive"]["footer"] = {
                "text": self.product_footer
            }

        return product_message

    def create_flow_message(self):
        """Create an advanced flow message."""
        if not self.flow_id:
            return

        flow_message = {
            "type": "interactive",
            "interactive": {
                "type": "flow",
                "header": {
                    "type": "text",
                    "text": self.flow_header_text or "Complete the form"
                },
                "body": {
                    "text": self.flow_body_text or "Please fill out the information below"
                },
                "action": {
                    "name": "flow",
                    "parameters": {
                        "flow_message_version": "3",
                        "flow_id": self.flow_id,
                        "flow_cta": self.flow_cta,
                        "flow_action": "navigate",
                        "flow_action_payload": self.flow_action_parameters
                    }
                }
            }
        }

        if self.flow_footer_text.strip():
            flow_message["interactive"]["footer"] = {
                "text": self.flow_footer_text
            }

        return flow_message

    def create_carousel_message(self):
        """Create a carousel message with multiple cards."""
        if not self.carousel_cards:
            return

        # WhatsApp carousel is implemented as multiple product cards
        carousel_message = {
            "type": "interactive",
            "interactive": {
                "type": "product_list",
                "header": {
                    "type": "text",
                    "text": "Browse our products"
                },
                "body": {
                    "text": "Select a product to learn more"
                },
                "action": {
                    "catalog_id": self.product_catalog_id,
                    "sections": [
                        {
                            "title": "Products",
                            "product_items": [
                                {
                                    "product_retailer_id": f"product_{i}"
                                }
                                for i, card in enumerate(self.carousel_cards)
                            ]
                        }
                    ]
                }
            }
        }

        return carousel_message
    
    def update_preview(self):
        """Update message preview based on current type."""
        if self.message_type == "text":
            self.preview_data = {
                "type": "text",
                "text": {"body": self.message_text}
            }
        
        elif self.message_type == "interactive_button":
            self.preview_data = {
                "type": "interactive",
                "interactive": {
                    "type": "button",
                    "body": {"text": self.button_body},
                    "action": {
                        "buttons": [
                            {
                                "type": "reply",
                                "reply": {
                                    "id": btn["id"],
                                    "title": btn["title"]
                                }
                            }
                            for btn in self.button_list
                        ]
                    }
                }
            }
            
            if self.button_header.strip():
                self.preview_data["interactive"]["header"] = {
                    "type": "text",
                    "text": self.button_header
                }
            
            if self.button_footer.strip():
                self.preview_data["interactive"]["footer"] = {
                    "text": self.button_footer
                }
        
        elif self.message_type == "interactive_list":
            self.preview_data = {
                "type": "interactive",
                "interactive": {
                    "type": "list",
                    "body": {"text": self.list_body},
                    "action": {
                        "button": self.list_button_text,
                        "sections": self.list_sections
                    }
                }
            }
            
            if self.list_header.strip():
                self.preview_data["interactive"]["header"] = {
                    "type": "text",
                    "text": self.list_header
                }
            
            if self.list_footer.strip():
                self.preview_data["interactive"]["footer"] = {
                    "text": self.list_footer
                }
        
        elif self.message_type == "template":
            self.preview_data = {
                "type": "template",
                "template": {
                    "name": self.template_name,
                    "language": {"code": self.template_language}
                }
            }
            
            if self.template_parameters:
                self.preview_data["template"]["components"] = [
                    {
                        "type": "body",
                        "parameters": [
                            {"type": "text", "text": param}
                            for param in self.template_parameters
                        ]
                    }
                ]
        
        elif self.message_type == "location":
            self.preview_data = {
                "type": "location",
                "location": {
                    "latitude": self.location_latitude,
                    "longitude": self.location_longitude,
                    "name": self.location_name,
                    "address": self.location_address
                }
            }
        
        elif self.message_type == "contact":
            self.preview_data = {
                "type": "contacts",
                "contacts": [
                    {
                        "name": {
                            "formatted_name": self.contact_name,
                            "first_name": self.contact_name.split()[0] if self.contact_name else ""
                        },
                        "phones": [
                            {
                                "phone": self.contact_phone,
                                "type": "MAIN"
                            }
                        ] if self.contact_phone else [],
                        "emails": [
                            {
                                "email": self.contact_email,
                                "type": "WORK"
                            }
                        ] if self.contact_email else [],
                        "org": {
                            "company": self.contact_organization
                        } if self.contact_organization else {}
                    }
                ]
            }

        elif self.message_type == "interactive_flow":
            self.preview_data = self.create_flow_message() or {}

        elif self.message_type == "product":
            self.preview_data = self.create_product_message() or {}

        elif self.message_type == "carousel":
            self.preview_data = self.create_carousel_message() or {}

        elif self.message_type == "media":
            self.preview_data = {
                "type": self.media_type,
                self.media_type: {
                    "link": self.media_url,
                    "caption": self.media_caption
                }
            }
    
    async def send_message(self):
        """Send the composed message."""
        if not self.recipient_phone.strip():
            self.error_message = "Recipient phone number is required"
            return

        if not self.selected_account_id:
            self.error_message = "Please select a WhatsApp account"
            return

        # Validate message content based on type
        if self.message_type == "text" and not self.message_text.strip():
            self.error_message = "Message text is required"
            return
        elif self.message_type == "interactive_button" and (not self.button_body.strip() or not self.button_list):
            self.error_message = "Button message requires body text and at least one button"
            return
        elif self.message_type == "interactive_list" and (not self.list_body.strip() or not self.list_sections):
            self.error_message = "List message requires body text and at least one section"
            return

        self.loading = True
        try:
            # Update preview data
            self.update_preview()

            with get_session() as session:
                # Get WhatsApp account
                account = session.get(WhatsAppAccount, self.selected_account_id)
                if not account:
                    self.error_message = "WhatsApp account not found"
                    return

                # Create message record
                new_message = Message(
                    account_id=self.selected_account_id,
                    message_type=self.message_type,
                    recipient_phone=self.recipient_phone.strip(),
                    content=self.preview_data,
                    status="pending"
                )

                session.add(new_message)
                session.commit()
                session.refresh(new_message)

                # Send via WhatsApp API
                try:
                    api_service = WhatsAppAPIService(account)

                    if self.message_type == "text":
                        result = api_service.send_text_message(
                            self.recipient_phone.strip(),
                            self.message_text
                        )
                    elif self.message_type == "interactive_button":
                        result = api_service.send_interactive_button_message(
                            self.recipient_phone.strip(),
                            self.button_body,
                            self.button_list,
                            self.button_header or None,
                            self.button_footer or None
                        )
                    elif self.message_type == "interactive_list":
                        result = api_service.send_interactive_list_message(
                            self.recipient_phone.strip(),
                            self.list_body,
                            self.list_button_text,
                            self.list_sections,
                            self.list_header or None,
                            self.list_footer or None
                        )
                    elif self.message_type == "template":
                        result = api_service.send_template_message(
                            self.recipient_phone.strip(),
                            self.template_name,
                            self.template_language,
                            self.template_parameters
                        )
                    elif self.message_type == "location":
                        result = api_service.send_location_message(
                            self.recipient_phone.strip(),
                            self.location_latitude,
                            self.location_longitude,
                            self.location_name or None,
                            self.location_address or None
                        )
                    elif self.message_type == "contact":
                        result = api_service.send_contact_message(
                            self.recipient_phone.strip(),
                            self.contact_name,
                            self.contact_phone,
                            self.contact_email or None,
                            self.contact_organization or None
                        )
                    elif self.message_type == "media":
                        result = api_service.send_media_message(
                            self.recipient_phone.strip(),
                            self.media_type,
                            self.media_url,
                            self.media_caption or None
                        )
                    else:
                        raise WhatsAppAPIError(f"Unsupported message type: {self.message_type}")

                    # Update message with API response
                    if result and "messages" in result:
                        message_id = result["messages"][0].get("id")
                        new_message.whatsapp_message_id = message_id
                        new_message.status = "sent"
                        new_message.sent_at = datetime.utcnow()
                        new_message.api_response = result
                    else:
                        new_message.status = "failed"
                        new_message.error_message = "No message ID returned from API"

                    session.commit()

                    self.success_message = f"Message sent successfully to {self.recipient_phone}"

                except WhatsAppAPIError as api_error:
                    new_message.status = "failed"
                    new_message.error_message = str(api_error)
                    session.commit()
                    self.error_message = f"Failed to send message: {str(api_error)}"
                    return

                self.show_compose_form = False
                self.clear_form()

                # Reload messages
                await self.load_messages()

        except Exception as e:
            self.error_message = f"Failed to send message: {str(e)}"
        finally:
            self.loading = False
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
    
    def set_message_type(self, msg_type: str):
        """Set message type and clear relevant fields."""
        self.message_type = msg_type
        self.clear_messages()
        self.update_preview()
    
    def set_recipient_phone(self, phone: str):
        """Set recipient phone number."""
        self.recipient_phone = phone
        self.clear_messages()
    
    def set_selected_account(self, account_id: int):
        """Set selected WhatsApp account."""
        self.selected_account_id = account_id
        self.clear_messages()
