{"bot_name": "E-commerce Shopping Bot", "bot_type": "ecommerce", "description": "E-commerce bot for testing shopping and order management flows", "test_scenarios": [{"name": "Product Search", "description": "Test product search and browsing", "expected_keywords": ["product", "search", "category", "browse", "items"], "messages": [{"text": "show products", "description": "Browse products", "wait_time": 3}, {"text": "search shoes", "description": "Search for specific product", "wait_time": 3}]}, {"name": "Cart Management", "description": "Test shopping cart operations", "expected_keywords": ["cart", "add", "remove", "checkout", "total"], "messages": [{"text": "add to cart", "description": "Add item to cart", "wait_time": 2}, {"text": "view cart", "description": "View cart contents", "wait_time": 2}, {"text": "checkout", "description": "Proceed to checkout", "wait_time": 3}]}, {"name": "Order Status", "description": "Test order tracking and status", "expected_keywords": ["order", "status", "tracking", "delivery", "shipped"], "messages": [{"text": "order status", "description": "Check order status", "wait_time": 3}, {"text": "track order", "description": "Track delivery", "wait_time": 3}]}], "test_settings": {"default_wait_time": 2, "response_timeout": 15, "scenario_delay": 4, "max_conversation_depth": 25, "retry_failed_messages": true, "log_detailed_responses": true}}