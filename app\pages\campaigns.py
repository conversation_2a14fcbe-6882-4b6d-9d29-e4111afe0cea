"""Campaign management pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header


def campaigns_page() -> rx.Component:
    """Campaigns overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Campaigns",
                "Manage your WhatsApp marketing campaigns",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "Create Campaign",
                    on_click=lambda: rx.redirect("/campaigns/create"),
                    color_scheme="blue"
                )
            ),
            
            rx.text("Campaigns page - Coming soon!"),
            
            spacing="6",
            width="100%"
        ),
        current_page="campaigns"
    )


def create_campaign_page() -> rx.Component:
    """Create new campaign page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Create Campaign",
                "Set up a new WhatsApp marketing campaign"
            ),
            
            rx.text("Create campaign page - Coming soon!"),
            
            spacing="6",
            width="100%"
        ),
        current_page="campaigns"
    )
