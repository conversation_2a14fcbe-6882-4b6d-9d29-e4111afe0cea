#!/usr/bin/env python3
"""
Test script for the enhanced bot testing functionality.
This script demonstrates how to use the JSON bot configuration system.
"""

import json
import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def create_sample_bot_config():
    """Create a comprehensive sample bot configuration."""
    config = {
        "bot_name": "Advanced Customer Service Bot",
        "bot_type": "customer_service",
        "description": "Comprehensive customer service bot for testing various conversation flows and scenarios",
        "test_scenarios": [
            {
                "name": "Welcome Flow",
                "description": "Test bot welcome and initial responses",
                "expected_keywords": ["welcome", "hello", "menu", "help", "options"],
                "messages": [
                    {
                        "text": "hi",
                        "description": "Test welcome trigger",
                        "wait_time": 2
                    },
                    {
                        "text": "hello",
                        "description": "Test welcome variant",
                        "wait_time": 2
                    },
                    {
                        "text": "help",
                        "description": "Test help command",
                        "wait_time": 3
                    }
                ]
            },
            {
                "name": "Menu Navigation",
                "description": "Test main menu and navigation options",
                "expected_keywords": ["menu", "option", "select", "choose", "category"],
                "messages": [
                    {
                        "text": "menu",
                        "description": "Test main menu access",
                        "wait_time": 3
                    },
                    {
                        "text": "1",
                        "description": "Test menu option selection",
                        "wait_time": 2
                    },
                    {
                        "text": "back",
                        "description": "Test back navigation",
                        "wait_time": 2
                    }
                ]
            },
            {
                "name": "Support Request",
                "description": "Test customer support conversation flow",
                "expected_keywords": ["support", "agent", "help", "contact", "assistance"],
                "messages": [
                    {
                        "text": "I need support",
                        "description": "Request support",
                        "wait_time": 3
                    },
                    {
                        "text": "talk to agent",
                        "description": "Request human agent",
                        "wait_time": 4
                    },
                    {
                        "text": "urgent issue",
                        "description": "Escalate to urgent support",
                        "wait_time": 3
                    }
                ]
            },
            {
                "name": "Product Inquiry",
                "description": "Test product information and pricing queries",
                "expected_keywords": ["product", "price", "cost", "information", "details"],
                "messages": [
                    {
                        "text": "product information",
                        "description": "Request product details",
                        "wait_time": 3
                    },
                    {
                        "text": "pricing",
                        "description": "Ask about pricing",
                        "wait_time": 3
                    },
                    {
                        "text": "features",
                        "description": "Inquire about features",
                        "wait_time": 2
                    }
                ]
            },
            {
                "name": "Error Handling",
                "description": "Test bot error handling and fallback responses",
                "expected_keywords": ["sorry", "understand", "help", "try again", "invalid"],
                "messages": [
                    {
                        "text": "invalid_command_xyz",
                        "description": "Test invalid input",
                        "wait_time": 2
                    },
                    {
                        "text": "random gibberish text 12345",
                        "description": "Test unrecognized input",
                        "wait_time": 2
                    },
                    {
                        "text": "reset",
                        "description": "Test bot reset command",
                        "wait_time": 2
                    }
                ]
            },
            {
                "name": "Business Hours Inquiry",
                "description": "Test business information queries",
                "expected_keywords": ["hours", "open", "closed", "schedule", "time"],
                "messages": [
                    {
                        "text": "what are your hours",
                        "description": "Test hours inquiry",
                        "wait_time": 3
                    },
                    {
                        "text": "are you open now",
                        "description": "Test current status inquiry",
                        "wait_time": 2
                    },
                    {
                        "text": "weekend hours",
                        "description": "Test specific schedule inquiry",
                        "wait_time": 3
                    }
                ]
            }
        ],
        "expected_responses": {
            "welcome_keywords": ["welcome", "hello", "menu", "help", "options"],
            "menu_keywords": ["option", "select", "choose", "menu", "category"],
            "support_keywords": ["support", "agent", "help", "contact", "assistance"],
            "product_keywords": ["product", "price", "cost", "information", "details"],
            "error_keywords": ["sorry", "understand", "help", "try again", "invalid"],
            "hours_keywords": ["hours", "open", "closed", "schedule", "time"]
        },
        "test_settings": {
            "default_wait_time": 2,
            "response_timeout": 10,
            "scenario_delay": 3,
            "max_conversation_depth": 20,
            "retry_failed_messages": True,
            "log_detailed_responses": True,
            "test_mode": True,
            "stop_on_first_failure": False,
            "parallel_testing": False
        }
    }
    
    return config

def create_ecommerce_bot_config():
    """Create an e-commerce bot configuration."""
    config = {
        "bot_name": "E-commerce Shopping Bot",
        "bot_type": "ecommerce",
        "description": "E-commerce bot for testing shopping and order management flows",
        "test_scenarios": [
            {
                "name": "Product Search",
                "description": "Test product search and browsing",
                "expected_keywords": ["product", "search", "category", "browse", "items"],
                "messages": [
                    {
                        "text": "show products",
                        "description": "Browse products",
                        "wait_time": 3
                    },
                    {
                        "text": "search shoes",
                        "description": "Search for specific product",
                        "wait_time": 3
                    }
                ]
            },
            {
                "name": "Cart Management",
                "description": "Test shopping cart operations",
                "expected_keywords": ["cart", "add", "remove", "checkout", "total"],
                "messages": [
                    {
                        "text": "add to cart",
                        "description": "Add item to cart",
                        "wait_time": 2
                    },
                    {
                        "text": "view cart",
                        "description": "View cart contents",
                        "wait_time": 2
                    },
                    {
                        "text": "checkout",
                        "description": "Proceed to checkout",
                        "wait_time": 3
                    }
                ]
            },
            {
                "name": "Order Status",
                "description": "Test order tracking and status",
                "expected_keywords": ["order", "status", "tracking", "delivery", "shipped"],
                "messages": [
                    {
                        "text": "order status",
                        "description": "Check order status",
                        "wait_time": 3
                    },
                    {
                        "text": "track order",
                        "description": "Track delivery",
                        "wait_time": 3
                    }
                ]
            }
        ],
        "test_settings": {
            "default_wait_time": 2,
            "response_timeout": 15,
            "scenario_delay": 4,
            "max_conversation_depth": 25,
            "retry_failed_messages": True,
            "log_detailed_responses": True
        }
    }
    
    return config

def save_config_to_file(config, filename):
    """Save configuration to JSON file."""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"✅ Configuration saved to {filename}")

def validate_config(config):
    """Validate bot configuration structure."""
    required_fields = ["bot_name", "bot_type", "test_scenarios"]
    
    for field in required_fields:
        if field not in config:
            print(f"❌ Missing required field: {field}")
            return False
    
    if not isinstance(config["test_scenarios"], list):
        print("❌ test_scenarios must be a list")
        return False
    
    for i, scenario in enumerate(config["test_scenarios"]):
        if "name" not in scenario:
            print(f"❌ Scenario {i} missing 'name' field")
            return False
        if "messages" not in scenario:
            print(f"❌ Scenario {i} missing 'messages' field")
            return False
        
        for j, message in enumerate(scenario["messages"]):
            if "text" not in message:
                print(f"❌ Scenario {i}, message {j} missing 'text' field")
                return False
    
    print("✅ Configuration validation passed")
    return True

def main():
    """Main function to create and save bot configurations."""
    print("🤖 Bot Configuration Generator")
    print("=" * 40)
    
    # Create sample configurations
    customer_service_config = create_sample_bot_config()
    ecommerce_config = create_ecommerce_bot_config()
    
    # Validate configurations
    print("\n📋 Validating configurations...")
    if validate_config(customer_service_config):
        save_config_to_file(customer_service_config, "customer_service_bot_config.json")
    
    if validate_config(ecommerce_config):
        save_config_to_file(ecommerce_config, "ecommerce_bot_config.json")
    
    # Update the existing sample config
    save_config_to_file(customer_service_config, "sample_bot_config.json")
    
    print("\n🎉 Bot configurations created successfully!")
    print("\nYou can now:")
    print("1. Upload these JSON files to the Reflex bot testing interface")
    print("2. Use them to test your WhatsApp bots")
    print("3. Modify them to match your specific bot requirements")
    
    print(f"\n📊 Customer Service Bot: {len(customer_service_config['test_scenarios'])} scenarios")
    print(f"📊 E-commerce Bot: {len(ecommerce_config['test_scenarios'])} scenarios")

if __name__ == "__main__":
    main()
