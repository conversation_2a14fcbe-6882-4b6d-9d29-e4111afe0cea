"""Main application entry point for WhatsApp Business Platform."""

import reflex as rx
from typing import List

# Import all pages
from .pages import (
    auth,
    dashboard,
    messages,
    campaigns,
    templates,
    flows,
    contacts,
    analytics,
    bot_testing,
    bulk_messaging,
    settings,
    admin
)

# Import components
from .components.layout import layout
from .components.navigation import navbar
from .state.auth_state import AuthState


class AppState(rx.State):
    """Main application state."""
    
    # Global app state
    is_loading: bool = False
    current_page: str = "dashboard"
    sidebar_open: bool = True
    theme: str = "light"
    
    # Notifications
    notifications: List[dict] = []
    
    def toggle_sidebar(self):
        """Toggle sidebar visibility."""
        self.sidebar_open = not self.sidebar_open
    
    def toggle_theme(self):
        """Toggle between light and dark theme."""
        self.theme = "dark" if self.theme == "light" else "light"
    
    def add_notification(self, message: str, type: str = "info"):
        """Add a notification to the queue."""
        notification = {
            "id": len(self.notifications) + 1,
            "message": message,
            "type": type,
            "timestamp": rx.moment().format("HH:mm:ss")
        }
        self.notifications.append(notification)
    
    def remove_notification(self, notification_id: int):
        """Remove a notification from the queue."""
        self.notifications = [
            n for n in self.notifications if n["id"] != notification_id
        ]


def index() -> rx.Component:
    """Landing page - redirects to dashboard if authenticated, otherwise to login."""
    from .pages.simple_dashboard import simple_dashboard_page
    return rx.cond(
        AuthState.is_authenticated,
        simple_dashboard_page(),
        auth.login_page()
    )


# Create the main app
app = rx.App(
    stylesheets=[
        "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap",
        "https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap",
    ],
    theme=rx.theme(
        appearance="light",
        has_background=True,
        radius="medium",
        accent_color="blue",
    )
)

# Add pages to the app
app.add_page(
    index,
    route="/",
    title="WhatsApp Business Platform",
    description="Professional WhatsApp Business API Management Platform"
)

# Authentication pages
app.add_page(
    auth.login_page,
    route="/login",
    title="Login - WhatsApp Business Platform"
)

app.add_page(
    auth.register_page,
    route="/register", 
    title="Register - WhatsApp Business Platform"
)

app.add_page(
    auth.forgot_password_page,
    route="/forgot-password",
    title="Forgot Password - WhatsApp Business Platform"
)

# Main application pages (protected)
app.add_page(
    dashboard.dashboard_page,
    route="/dashboard",
    title="Dashboard - WhatsApp Business Platform"
)

app.add_page(
    messages.messages_page,
    route="/messages",
    title="Messages - WhatsApp Business Platform"
)

app.add_page(
    messages.compose_page,
    route="/messages/compose",
    title="Compose Message - WhatsApp Business Platform"
)

app.add_page(
    campaigns.campaigns_page,
    route="/campaigns",
    title="Campaigns - WhatsApp Business Platform"
)

app.add_page(
    campaigns.create_campaign_page,
    route="/campaigns/create",
    title="Create Campaign - WhatsApp Business Platform"
)

app.add_page(
    templates.templates_page,
    route="/templates",
    title="Templates - WhatsApp Business Platform"
)

app.add_page(
    templates.create_template_page,
    route="/templates/create",
    title="Create Template - WhatsApp Business Platform"
)

app.add_page(
    flows.flows_page,
    route="/flows",
    title="Flows - WhatsApp Business Platform"
)

app.add_page(
    flows.flow_builder_page,
    route="/flows/builder",
    title="Flow Builder - WhatsApp Business Platform"
)

app.add_page(
    contacts.contacts_page,
    route="/contacts",
    title="Contacts - WhatsApp Business Platform"
)

app.add_page(
    analytics.analytics_page,
    route="/analytics",
    title="Analytics - WhatsApp Business Platform"
)

app.add_page(
    bot_testing.bot_testing_page,
    route="/bot-testing",
    title="Bot Testing - WhatsApp Business Platform"
)

app.add_page(
    bot_testing.create_bot_test_page,
    route="/bot-testing/create",
    title="Create Bot Test - WhatsApp Business Platform"
)

app.add_page(
    bulk_messaging.bulk_messaging_page,
    route="/bulk-messaging",
    title="Bulk Messaging - WhatsApp Business Platform"
)

app.add_page(
    settings.settings_page,
    route="/settings",
    title="Settings - WhatsApp Business Platform"
)

app.add_page(
    settings.whatsapp_settings_page,
    route="/settings/whatsapp",
    title="WhatsApp Settings - WhatsApp Business Platform"
)

# Admin pages
app.add_page(
    admin.admin_dashboard_page,
    route="/admin",
    title="Admin Dashboard - WhatsApp Business Platform"
)

app.add_page(
    admin.user_management_page,
    route="/admin/users",
    title="User Management - WhatsApp Business Platform"
)

# Health check endpoint (moved from /api/health to avoid NextJS conflict)
app.add_page(
    lambda: rx.text("API Health Check: OK"),
    route="/health",
    title="API Health Check"
)

# Error pages
def not_found_page() -> rx.Component:
    """404 Not Found page."""
    return layout(
        rx.vstack(
            rx.heading("404 - Page Not Found", size="8"),
            rx.text("The page you're looking for doesn't exist."),
            rx.link(
                rx.button("Go to Dashboard", color_scheme="blue"),
                href="/dashboard"
            ),
            spacing="4",
            align="center",
            justify="center",
            min_height="60vh"
        )
    )

app.add_page(
    not_found_page,
    route="/404",
    title="Page Not Found - WhatsApp Business Platform"
)

# Set up custom 404 handler
app._404 = not_found_page
