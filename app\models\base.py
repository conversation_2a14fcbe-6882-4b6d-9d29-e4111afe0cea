"""Base database model with common fields and functionality."""

import reflex as rx
from sqlmodel import SQLModel, Field
from sqlalchemy import <PERSON>umn, <PERSON>te<PERSON>, DateTime, String, Boolean
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional


class BaseModel(SQLModel):
    """Base model with common fields for all database tables."""

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: Optional[datetime] = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now())
    )
    updated_at: Optional[datetime] = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    )
    is_active: bool = Field(default=True)
    
    # Soft delete support
    deleted_at: Optional[datetime] = Field(default=None)

    # Audit fields
    created_by: Optional[str] = Field(default=None, max_length=255)
    updated_by: Optional[str] = Field(default=None, max_length=255)
    
    class Config:
        """Model configuration."""
        arbitrary_types_allowed = True
        
    def soft_delete(self, user_id: Optional[str] = None) -> None:
        """Soft delete the record."""
        self.deleted_at = datetime.utcnow()
        self.is_active = False
        if user_id:
            self.updated_by = user_id
    
    def restore(self, user_id: Optional[str] = None) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
        self.is_active = True
        if user_id:
            self.updated_by = user_id
    
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None
    
    def to_dict(self) -> dict:
        """Convert model to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


class TimestampMixin:
    """Mixin for models that need timestamp tracking."""
    
    created_at: datetime = Column(DateTime(timezone=True), server_default=func.now())
    updated_at: datetime = Column(DateTime(timezone=True), onupdate=func.now())


class SoftDeleteMixin:
    """Mixin for models that support soft deletion."""
    
    deleted_at: Optional[datetime] = Column(DateTime(timezone=True), nullable=True)
    is_active: bool = Column(Boolean, default=True)
    
    def soft_delete(self) -> None:
        """Soft delete the record."""
        self.deleted_at = datetime.utcnow()
        self.is_active = False
    
    def restore(self) -> None:
        """Restore a soft-deleted record."""
        self.deleted_at = None
        self.is_active = True
    
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None


class AuditMixin:
    """Mixin for models that need audit trail."""
    
    created_by: Optional[str] = Column(String(255), nullable=True)
    updated_by: Optional[str] = Column(String(255), nullable=True)
    
    def set_created_by(self, user_id: str) -> None:
        """Set the user who created this record."""
        self.created_by = user_id
    
    def set_updated_by(self, user_id: str) -> None:
        """Set the user who last updated this record."""
        self.updated_by = user_id
