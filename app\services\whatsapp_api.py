"""WhatsApp Business API integration service."""

import requests
import json
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

from ..models.whatsapp import WhatsAppAccount, Message
from ..database import get_session

logger = logging.getLogger(__name__)


class WhatsAppAPIError(Exception):
    """Custom exception for WhatsApp API errors."""
    pass


class WhatsAppAPIService:
    """Service for interacting with WhatsApp Business API."""
    
    def __init__(self, account: WhatsAppAccount):
        """Initialize with WhatsApp account credentials."""
        self.account = account
        self.base_url = "https://graph.facebook.com/v21.0"
        self.phone_number_id = account.phone_number_id
        self.access_token = account.access_token
        
        # Rate limiting
        self.rate_limit_per_second = 10
        self.rate_limit_per_minute = 600
        self.rate_limit_per_hour = 36000
        
        # Request session
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        })
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers."""
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    def _get_messages_url(self) -> str:
        """Get messages endpoint URL."""
        return f"{self.base_url}/{self.phone_number_id}/messages"
    
    def _get_media_url(self) -> str:
        """Get media endpoint URL."""
        return f"{self.base_url}/{self.phone_number_id}/media"
    
    def send_text_message(self, to: str, text: str) -> Dict[str, Any]:
        """Send a text message."""
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "text",
            "text": {
                "body": text
            }
        }
        
        return self._send_message(payload)
    
    def send_template_message(self, to: str, template_name: str, language_code: str = "en_US", 
                            parameters: Optional[List[str]] = None) -> Dict[str, Any]:
        """Send a template message."""
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "template",
            "template": {
                "name": template_name,
                "language": {
                    "code": language_code
                }
            }
        }
        
        if parameters:
            payload["template"]["components"] = [
                {
                    "type": "body",
                    "parameters": [
                        {
                            "type": "text",
                            "text": param
                        }
                        for param in parameters
                    ]
                }
            ]
        
        return self._send_message(payload)
    
    def send_interactive_button_message(self, to: str, body_text: str, buttons: List[Dict[str, str]],
                                      header_text: Optional[str] = None, 
                                      footer_text: Optional[str] = None) -> Dict[str, Any]:
        """Send an interactive button message."""
        if len(buttons) > 3:
            raise WhatsAppAPIError("Maximum 3 buttons allowed")
        
        interactive_data = {
            "type": "button",
            "body": {
                "text": body_text
            },
            "action": {
                "buttons": [
                    {
                        "type": "reply",
                        "reply": {
                            "id": btn["id"],
                            "title": btn["title"]
                        }
                    }
                    for btn in buttons
                ]
            }
        }
        
        if header_text:
            interactive_data["header"] = {
                "type": "text",
                "text": header_text
            }
        
        if footer_text:
            interactive_data["footer"] = {
                "text": footer_text
            }
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "interactive",
            "interactive": interactive_data
        }
        
        return self._send_message(payload)
    
    def send_interactive_list_message(self, to: str, body_text: str, button_text: str,
                                    sections: List[Dict[str, Any]], 
                                    header_text: Optional[str] = None,
                                    footer_text: Optional[str] = None) -> Dict[str, Any]:
        """Send an interactive list message."""
        if len(sections) > 10:
            raise WhatsAppAPIError("Maximum 10 sections allowed")
        
        interactive_data = {
            "type": "list",
            "body": {
                "text": body_text
            },
            "action": {
                "button": button_text,
                "sections": sections
            }
        }
        
        if header_text:
            interactive_data["header"] = {
                "type": "text",
                "text": header_text
            }
        
        if footer_text:
            interactive_data["footer"] = {
                "text": footer_text
            }
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "interactive",
            "interactive": interactive_data
        }
        
        return self._send_message(payload)
    
    def send_location_message(self, to: str, latitude: float, longitude: float,
                            name: Optional[str] = None, address: Optional[str] = None) -> Dict[str, Any]:
        """Send a location message."""
        location_data = {
            "latitude": latitude,
            "longitude": longitude
        }
        
        if name:
            location_data["name"] = name
        if address:
            location_data["address"] = address
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "location",
            "location": location_data
        }
        
        return self._send_message(payload)
    
    def send_contact_message(self, to: str, contact_name: str, phone_number: str,
                           email: Optional[str] = None, organization: Optional[str] = None) -> Dict[str, Any]:
        """Send a contact message."""
        contact_data = {
            "name": {
                "formatted_name": contact_name,
                "first_name": contact_name.split()[0] if contact_name else ""
            },
            "phones": [
                {
                    "phone": phone_number,
                    "type": "MAIN"
                }
            ]
        }
        
        if email:
            contact_data["emails"] = [
                {
                    "email": email,
                    "type": "WORK"
                }
            ]
        
        if organization:
            contact_data["org"] = {
                "company": organization
            }
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": "contacts",
            "contacts": [contact_data]
        }
        
        return self._send_message(payload)
    
    def send_media_message(self, to: str, media_type: str, media_url: str, 
                         caption: Optional[str] = None) -> Dict[str, Any]:
        """Send a media message (image, video, document, audio)."""
        if media_type not in ["image", "video", "document", "audio"]:
            raise WhatsAppAPIError(f"Unsupported media type: {media_type}")
        
        media_data = {
            "link": media_url
        }
        
        if caption and media_type in ["image", "video", "document"]:
            media_data["caption"] = caption
        
        payload = {
            "messaging_product": "whatsapp",
            "recipient_type": "individual",
            "to": to,
            "type": media_type,
            media_type: media_data
        }
        
        return self._send_message(payload)
    
    def _send_message(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Send message to WhatsApp API."""
        try:
            logger.info(f"Sending message to {payload.get('to')}: {payload.get('type')}")
            
            response = self.session.post(
                self._get_messages_url(),
                json=payload,
                timeout=30
            )
            
            response.raise_for_status()
            result = response.json()
            
            logger.info(f"Message sent successfully: {result}")
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send message: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    logger.error(f"API Error Response: {error_data}")
                    raise WhatsAppAPIError(f"API Error: {error_data.get('error', {}).get('message', str(e))}")
                except:
                    pass
            raise WhatsAppAPIError(f"Request failed: {str(e)}")
    
    def get_message_status(self, message_id: str) -> Dict[str, Any]:
        """Get message delivery status."""
        try:
            url = f"{self.base_url}/{message_id}"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get message status: {str(e)}")
            raise WhatsAppAPIError(f"Failed to get message status: {str(e)}")
    
    def upload_media(self, media_file_path: str, media_type: str) -> str:
        """Upload media file and return media ID."""
        try:
            with open(media_file_path, 'rb') as media_file:
                files = {
                    'file': media_file,
                    'type': media_type,
                    'messaging_product': 'whatsapp'
                }
                
                headers = {
                    "Authorization": f"Bearer {self.access_token}"
                }
                
                response = requests.post(
                    self._get_media_url(),
                    files=files,
                    headers=headers,
                    timeout=60
                )
                
                response.raise_for_status()
                result = response.json()
                
                return result.get('id')
                
        except Exception as e:
            logger.error(f"Failed to upload media: {str(e)}")
            raise WhatsAppAPIError(f"Media upload failed: {str(e)}")


class BulkMessageSender:
    """Service for sending bulk messages with rate limiting."""
    
    def __init__(self, api_service: WhatsAppAPIService):
        """Initialize with WhatsApp API service."""
        self.api_service = api_service
        self.delay_between_messages = 1.0
        self.max_messages_per_minute = 30
        self.sent_count = 0
        self.failed_count = 0
        self.start_time = None
    
    async def send_bulk_messages(self, recipients: List[Dict[str, str]], 
                               message_data: Dict[str, Any],
                               progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """Send messages to multiple recipients with rate limiting."""
        self.sent_count = 0
        self.failed_count = 0
        self.start_time = datetime.now()
        
        total_recipients = len(recipients)
        results = []
        
        for i, recipient in enumerate(recipients):
            try:
                # Rate limiting
                if i > 0:
                    await asyncio.sleep(self.delay_between_messages)
                
                # Send message based on type
                result = await self._send_single_message(recipient, message_data)
                results.append({
                    "recipient": recipient,
                    "status": "sent",
                    "message_id": result.get("messages", [{}])[0].get("id"),
                    "result": result
                })
                self.sent_count += 1
                
            except Exception as e:
                logger.error(f"Failed to send to {recipient.get('phone_number')}: {str(e)}")
                results.append({
                    "recipient": recipient,
                    "status": "failed",
                    "error": str(e)
                })
                self.failed_count += 1
            
            # Progress callback
            if progress_callback:
                progress = int(((i + 1) / total_recipients) * 100)
                await progress_callback(progress, self.sent_count, self.failed_count)
        
        return {
            "total_recipients": total_recipients,
            "sent_count": self.sent_count,
            "failed_count": self.failed_count,
            "duration": (datetime.now() - self.start_time).total_seconds(),
            "results": results
        }
    
    async def _send_single_message(self, recipient: Dict[str, str], 
                                 message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send a single message."""
        phone_number = recipient["phone_number"]
        message_type = message_data.get("type", "text")
        
        if message_type == "text":
            return self.api_service.send_text_message(
                phone_number, 
                message_data["text"]["body"]
            )
        elif message_type == "template":
            template = message_data["template"]
            parameters = []
            if "components" in template:
                for component in template["components"]:
                    if component["type"] == "body" and "parameters" in component:
                        parameters = [p["text"] for p in component["parameters"]]
            
            return self.api_service.send_template_message(
                phone_number,
                template["name"],
                template["language"]["code"],
                parameters
            )
        elif message_type == "interactive":
            interactive = message_data["interactive"]
            if interactive["type"] == "button":
                return self.api_service.send_interactive_button_message(
                    phone_number,
                    interactive["body"]["text"],
                    [btn["reply"] for btn in interactive["action"]["buttons"]],
                    interactive.get("header", {}).get("text"),
                    interactive.get("footer", {}).get("text")
                )
            elif interactive["type"] == "list":
                return self.api_service.send_interactive_list_message(
                    phone_number,
                    interactive["body"]["text"],
                    interactive["action"]["button"],
                    interactive["action"]["sections"],
                    interactive.get("header", {}).get("text"),
                    interactive.get("footer", {}).get("text")
                )
        
        raise WhatsAppAPIError(f"Unsupported message type: {message_type}")
