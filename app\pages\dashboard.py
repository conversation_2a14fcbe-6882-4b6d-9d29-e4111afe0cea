"""Dashboard page with overview and quick actions."""

import reflex as rx
from ..components.layout import protected_layout
from ..state.auth_state import AuthState


class DashboardState(rx.State):
    """Dashboard state management."""
    
    # Dashboard metrics
    total_messages: int = 1247
    total_campaigns: int = 23
    active_templates: int = 8
    bot_tests_today: int = 15
    
    # Recent activity
    recent_activities: list = [
        {"type": "message", "description": "Sent 150 messages to Customer Group A", "time": "2 minutes ago"},
        {"type": "campaign", "description": "Campaign 'Holiday Sale' completed", "time": "1 hour ago"},
        {"type": "template", "description": "Template 'Welcome Message' approved", "time": "3 hours ago"},
        {"type": "bot", "description": "Bot test completed successfully", "time": "5 hours ago"},
    ]
    
    # Quick stats
    messages_today: int = 89
    response_rate: float = 87.5
    avg_response_time: str = "2.3 min"
    active_channels: int = 3


def dashboard_page() -> rx.Component:
    """Main dashboard page."""
    return protected_layout(
        rx.vstack(
            # Page header
            rx.hstack(
                rx.vstack(
                    rx.heading("Dashboard", size="8"),
                    rx.text(
                        rx.cond(
                            AuthState.current_user,
                            f"Welcome back, {AuthState.current_user['first_name']}!",
                            "Welcome back, User!"
                        ),
                        color="gray"
                    ),
                    spacing="1",
                    align="start"
                ),
                rx.spacer(),
                rx.hstack(
                    rx.button(
                        rx.icon("plus", size=16),
                        "New Campaign",
                        on_click=lambda: rx.redirect("/campaigns/create"),
                        color_scheme="blue"
                    ),
                    rx.button(
                        rx.icon("message-circle", size=16),
                        "Send Message",
                        on_click=lambda: rx.redirect("/messages/compose"),
                        variant="outline"
                    ),
                    spacing="3"
                ),
                width="100%",
                justify="between",
                align="start"
            ),
            
            # Metrics cards
            rx.grid(
                # Total Messages
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("message-square", size=20, color="blue"),
                            rx.text("Total Messages", size="sm", color="gray"),
                            spacing="2",
                            align="center"
                        ),
                        rx.text(
                            DashboardState.total_messages,
                            size="6",
                            weight="bold"
                        ),
                        rx.text(
                            f"+{DashboardState.messages_today} today",
                            size="xs",
                            color="green"
                        ),
                        spacing="2",
                        align="start"
                    ),
                    padding="4"
                ),
                
                # Total Campaigns
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("megaphone", size=20, color="green"),
                            rx.text("Campaigns", size="sm", color="gray"),
                            spacing="2",
                            align="center"
                        ),
                        rx.text(
                            DashboardState.total_campaigns,
                            size="6",
                            weight="bold"
                        ),
                        rx.text(
                            "3 active",
                            size="xs",
                            color="blue"
                        ),
                        spacing="2",
                        align="start"
                    ),
                    padding="4"
                ),
                
                # Active Templates
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("file-text", size=20, color="purple"),
                            rx.text("Templates", size="sm", color="gray"),
                            spacing="2",
                            align="center"
                        ),
                        rx.text(
                            DashboardState.active_templates,
                            size="6",
                            weight="bold"
                        ),
                        rx.text(
                            "2 pending approval",
                            size="xs",
                            color="orange"
                        ),
                        spacing="2",
                        align="start"
                    ),
                    padding="4"
                ),
                
                # Bot Tests
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.icon("bot", size=20, color="red"),
                            rx.text("Bot Tests", size="sm", color="gray"),
                            spacing="2",
                            align="center"
                        ),
                        rx.text(
                            DashboardState.bot_tests_today,
                            size="6",
                            weight="bold"
                        ),
                        rx.text(
                            "Today",
                            size="xs",
                            color="gray"
                        ),
                        spacing="2",
                        align="start"
                    ),
                    padding="4"
                ),
                
                columns="4",
                spacing="4",
                width="100%"
            ),
            
            # Main content grid
            rx.grid(
                # Recent Activity
                rx.card(
                    rx.vstack(
                        rx.hstack(
                            rx.heading("Recent Activity", size="5"),
                            rx.spacer(),
                            rx.link(
                                "View all",
                                href="/analytics",
                                color="blue",
                                size="sm"
                            ),
                            width="100%",
                            justify="between"
                        ),
                        
                        rx.vstack(
                            rx.foreach(
                                DashboardState.recent_activities,
                                lambda activity: rx.hstack(
                                    rx.icon(
                                        rx.cond(
                                            activity["type"] == "message",
                                            "message-circle",
                                            rx.cond(
                                                activity["type"] == "campaign",
                                                "megaphone",
                                                rx.cond(
                                                    activity["type"] == "template",
                                                    "file-text",
                                                    "bot"
                                                )
                                            )
                                        ),
                                        size=16,
                                        color="gray"
                                    ),
                                    rx.vstack(
                                        rx.text(activity["description"], size="sm"),
                                        rx.text(activity["time"], size="xs", color="gray"),
                                        spacing="1",
                                        align="start"
                                    ),
                                    spacing="3",
                                    align="start",
                                    width="100%"
                                )
                            ),
                            spacing="3",
                            width="100%"
                        ),
                        
                        spacing="4",
                        align="start",
                        width="100%"
                    ),
                    padding="6"
                ),
                
                # Quick Stats
                rx.card(
                    rx.vstack(
                        rx.heading("Quick Stats", size="5"),
                        
                        rx.vstack(
                            # Response Rate
                            rx.vstack(
                                rx.hstack(
                                    rx.text("Response Rate", size="sm"),
                                    rx.spacer(),
                                    rx.text(f"{DashboardState.response_rate}%", size="sm", weight="bold"),
                                    width="100%",
                                    justify="between"
                                ),
                                rx.progress(value=DashboardState.response_rate, width="100%"),
                                spacing="2",
                                width="100%"
                            ),
                            
                            # Average Response Time
                            rx.hstack(
                                rx.text("Avg Response Time", size="sm"),
                                rx.spacer(),
                                rx.text(DashboardState.avg_response_time, size="sm", weight="bold"),
                                width="100%",
                                justify="between"
                            ),
                            
                            # Active Channels
                            rx.hstack(
                                rx.text("Active Channels", size="sm"),
                                rx.spacer(),
                                rx.text(DashboardState.active_channels, size="sm", weight="bold"),
                                width="100%",
                                justify="between"
                            ),
                            
                            spacing="4",
                            width="100%"
                        ),
                        
                        spacing="4",
                        align="start",
                        width="100%"
                    ),
                    padding="6"
                ),
                
                columns="2",
                spacing="6",
                width="100%"
            ),
            
            # Quick Actions
            rx.card(
                rx.vstack(
                    rx.heading("Quick Actions", size="5"),
                    
                    rx.grid(
                        rx.button(
                            rx.vstack(
                                rx.icon("message-circle", size=24),
                                rx.text("Send Message", size="sm"),
                                spacing="2",
                                align="center"
                            ),
                            on_click=lambda: rx.redirect("/messages/compose"),
                            variant="outline",
                            height="80px",
                            width="100%"
                        ),
                        
                        rx.button(
                            rx.vstack(
                                rx.icon("megaphone", size=24),
                                rx.text("Create Campaign", size="sm"),
                                spacing="2",
                                align="center"
                            ),
                            on_click=lambda: rx.redirect("/campaigns/create"),
                            variant="outline",
                            height="80px",
                            width="100%"
                        ),
                        
                        rx.button(
                            rx.vstack(
                                rx.icon("file-text", size=24),
                                rx.text("New Template", size="sm"),
                                spacing="2",
                                align="center"
                            ),
                            on_click=lambda: rx.redirect("/templates/create"),
                            variant="outline",
                            height="80px",
                            width="100%"
                        ),
                        
                        rx.button(
                            rx.vstack(
                                rx.icon("bot", size=24),
                                rx.text("Test Bot", size="sm"),
                                spacing="2",
                                align="center"
                            ),
                            on_click=lambda: rx.redirect("/bot-testing"),
                            variant="outline",
                            height="80px",
                            width="100%"
                        ),
                        
                        columns="4",
                        spacing="4",
                        width="100%"
                    ),
                    
                    spacing="4",
                    align="start",
                    width="100%"
                ),
                padding="6"
            ),
            
            spacing="6",
            width="100%"
        ),
        current_page="dashboard"
    )
