"""Messages pages - compose, send, and manage messages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.message_state import MessageState


def message_card(message: dict) -> rx.Component:
    """Message card component."""
    status_color = {
        "sent": "green",
        "delivered": "blue",
        "read": "purple",
        "failed": "red",
        "pending": "yellow"
    }.get(message["status"], "gray")

    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.vstack(
                    rx.text(f"To: {message['recipient_phone']}", weight="bold"),
                    rx.text(f"Type: {message['message_type']}", color="gray", size="sm"),
                    align="start",
                    spacing="1"
                ),
                rx.spacer(),
                rx.badge(message["status"], color_scheme=status_color),
                align="center",
                width="100%"
            ),

            rx.text(
                str(message["content"])[:100] + "..." if len(str(message["content"])) > 100 else str(message["content"]),
                color="gray",
                size="sm"
            ),

            rx.text(
                f"Sent: {message['sent_at'][:16] if message['sent_at'] else 'Not sent'}",
                size="xs",
                color="gray"
            ),

            align="start",
            spacing="2",
            width="100%"
        ),
        width="100%"
    )


def message_type_selector() -> rx.Component:
    """Message type selector component."""
    return rx.vstack(
        rx.text("Message Type:", weight="bold"),
        rx.select(
            [
                "text",
                "interactive_button",
                "interactive_list",
                "template",
                "location",
                "contact",
                "media"
            ],
            value=MessageState.message_type,
            on_change=MessageState.set_message_type,
            width="100%"
        ),
        spacing="2",
        width="100%"
    )


def text_message_form() -> rx.Component:
    """Text message form."""
    return rx.vstack(
        rx.text("Message Text:", weight="bold"),
        rx.text_area(
            placeholder="Enter your message...",
            value=MessageState.message_text,
            on_change=MessageState.set_message_text,
            height="100px",
            width="100%"
        ),
        spacing="2",
        width="100%"
    )


def button_message_form() -> rx.Component:
    """Interactive button message form."""
    return rx.vstack(
        rx.text("Button Message:", weight="bold", size="lg"),

        rx.grid(
            rx.vstack(
                rx.text("Header (optional):", weight="bold"),
                rx.input(
                    placeholder="Header text",
                    value=MessageState.button_header,
                    on_change=MessageState.set_button_header,
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),

            rx.vstack(
                rx.text("Footer (optional):", weight="bold"),
                rx.input(
                    placeholder="Footer text",
                    value=MessageState.button_footer,
                    on_change=MessageState.set_button_footer,
                    width="100%"
                ),
                spacing="2",
                width="100%"
            ),

            columns="2",
            spacing="4",
            width="100%"
        ),

        rx.vstack(
            rx.text("Body Text:", weight="bold"),
            rx.text_area(
                placeholder="Message body...",
                value=MessageState.button_body,
                on_change=MessageState.set_button_body,
                height="80px",
                width="100%"
            ),
            spacing="2",
            width="100%"
        ),

        # Buttons section
        rx.vstack(
            rx.text("Buttons (max 3):", weight="bold"),

            rx.hstack(
                rx.input(
                    placeholder="Button text",
                    value=MessageState.current_button_text,
                    on_change=MessageState.set_current_button_text,
                    width="200px"
                ),
                rx.input(
                    placeholder="Button ID",
                    value=MessageState.current_button_id,
                    on_change=MessageState.set_current_button_id,
                    width="150px"
                ),
                rx.button(
                    "Add Button",
                    on_click=MessageState.add_button,
                    size="sm"
                ),
                spacing="2"
            ),

            rx.cond(
                MessageState.button_list.length() > 0,
                rx.vstack(
                    rx.foreach(
                        MessageState.button_list,
                        lambda btn, idx: rx.hstack(
                            rx.text(f"{btn['title']} (ID: {btn['id']})", size="sm"),
                            rx.button(
                                "Remove",
                                on_click=MessageState.remove_button(idx),
                                size="xs",
                                color_scheme="red"
                            ),
                            justify="between",
                            width="100%"
                        )
                    ),
                    spacing="2",
                    width="100%"
                )
            ),

            spacing="3",
            width="100%"
        ),

        spacing="4",
        width="100%"
    )


def compose_message_form() -> rx.Component:
    """Message composition form."""
    return rx.card(
        rx.vstack(
            rx.heading("Compose Message", size="lg"),

            # Recipient and account selection
            rx.grid(
                rx.vstack(
                    rx.text("Recipient Phone:", weight="bold"),
                    rx.input(
                        placeholder="+**********",
                        value=MessageState.recipient_phone,
                        on_change=MessageState.set_recipient_phone,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                rx.vstack(
                    rx.text("WhatsApp Account:", weight="bold"),
                    rx.select(
                        [f"{acc['account_name']} ({acc['phone_number_id']})" for acc in MessageState.whatsapp_accounts],
                        placeholder="Select account",
                        on_change=lambda value: MessageState.set_selected_account(
                            MessageState.whatsapp_accounts[int(value)]["id"] if value.isdigit() else None
                        ),
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                ),

                columns="2",
                spacing="4",
                width="100%"
            ),

            # Message type selector
            message_type_selector(),

            # Dynamic form based on message type
            rx.cond(
                MessageState.message_type == "text",
                text_message_form()
            ),
            rx.cond(
                MessageState.message_type == "interactive_button",
                button_message_form()
            ),
            rx.cond(
                MessageState.message_type == "template",
                rx.vstack(
                    rx.text("Template Message:", weight="bold"),
                    rx.input(
                        placeholder="Template name",
                        value=MessageState.template_name,
                        on_change=MessageState.set_template_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),
            rx.cond(
                MessageState.message_type == "location",
                rx.vstack(
                    rx.text("Location Message:", weight="bold"),
                    rx.grid(
                        rx.input(
                            placeholder="Latitude",
                            value=str(MessageState.location_latitude),
                            on_change=lambda val: MessageState.set_location_latitude(float(val) if val else 0.0),
                            width="100%"
                        ),
                        rx.input(
                            placeholder="Longitude",
                            value=str(MessageState.location_longitude),
                            on_change=lambda val: MessageState.set_location_longitude(float(val) if val else 0.0),
                            width="100%"
                        ),
                        columns="2",
                        spacing="2",
                        width="100%"
                    ),
                    rx.input(
                        placeholder="Location name",
                        value=MessageState.location_name,
                        on_change=MessageState.set_location_name,
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),

            # Message preview
            rx.cond(
                MessageState.preview_data != {},
                rx.vstack(
                    rx.text("Message Preview:", weight="bold"),
                    rx.code_block(
                        rx.text(MessageState.preview_data),
                        language="json",
                        width="100%"
                    ),
                    spacing="2",
                    width="100%"
                )
            ),

            # Action buttons
            rx.hstack(
                rx.button(
                    "Cancel",
                    on_click=MessageState.toggle_compose_form,
                    variant="outline"
                ),
                rx.button(
                    rx.cond(
                        MessageState.loading,
                        rx.hstack(
                            rx.spinner(size="sm"),
                            "Sending...",
                            spacing="2"
                        ),
                        "Send Message"
                    ),
                    on_click=MessageState.send_message,
                    disabled=MessageState.loading,
                    color_scheme="blue"
                ),
                spacing="2"
            ),

            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def messages_page() -> rx.Component:
    """Messages overview page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Messages",
                "Send and manage WhatsApp messages",
                actions=rx.button(
                    rx.icon("plus", size=16),
                    "Compose Message",
                    on_click=MessageState.toggle_compose_form,
                    color_scheme="blue"
                )
            ),

            # Error/Success messages
            rx.cond(
                MessageState.error_message != "",
                rx.callout(
                    MessageState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),
            rx.cond(
                MessageState.success_message != "",
                rx.callout(
                    MessageState.success_message,
                    icon="check-circle",
                    color_scheme="green",
                    width="100%"
                )
            ),

            # Compose form
            rx.cond(
                MessageState.show_compose_form,
                compose_message_form()
            ),

            # Messages list
            rx.vstack(
                rx.hstack(
                    rx.heading("Recent Messages", size="lg"),
                    rx.spacer(),
                    rx.button(
                        rx.icon("refresh-cw", size=16),
                        "Refresh",
                        on_click=MessageState.load_messages,
                        variant="outline",
                        size="sm"
                    ),
                    width="100%",
                    align="center"
                ),

                rx.cond(
                    MessageState.loading,
                    rx.center(rx.spinner(size="lg"), height="200px"),
                    rx.cond(
                        MessageState.messages.length() > 0,
                        rx.vstack(
                            rx.foreach(
                                MessageState.messages,
                                message_card
                            ),
                            spacing="3",
                            width="100%"
                        ),
                        rx.center(
                            rx.vstack(
                                rx.icon("message-circle", size=48, color="gray"),
                                rx.text("No messages found", color="gray"),
                                rx.text("Send your first message to get started", size="sm", color="gray"),
                                spacing="2"
                            ),
                            height="200px"
                        )
                    )
                ),

                spacing="4",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        current_page="messages",
        on_mount=MessageState.load_messages
    )


def compose_page() -> rx.Component:
    """Compose new message page."""
    return protected_layout(
        rx.vstack(
            page_header(
                "Compose Message",
                "Create and send a new WhatsApp message"
            ),
            
            rx.text("Compose message page - Coming soon!"),
            
            spacing="6",
            width="100%"
        ),
        current_page="messages"
    )
