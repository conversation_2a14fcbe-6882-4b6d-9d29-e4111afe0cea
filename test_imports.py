#!/usr/bin/env python3
"""Test script to check if all imports work correctly."""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and print result."""
    try:
        __import__(module_name)
        print(f"✅ {description}: OK")
        return True
    except Exception as e:
        print(f"❌ {description}: FAILED - {e}")
        traceback.print_exc()
        return False

def main():
    """Run all import tests."""
    print("🧪 Testing imports for WhatsApp Business Platform...")
    print("=" * 60)
    
    tests = [
        ("reflex", "Reflex framework"),
        ("app.models.base", "Base models"),
        ("app.models.user", "User models"),
        ("app.models.whatsapp", "WhatsApp models"),
        ("app.models.bot", "Bot models"),
        ("app.models.analytics", "Analytics models"),
        ("app.state.auth_state", "Auth state"),
        ("app.state.bot_testing_state", "Bot testing state"),
        ("app.state.bulk_messaging_state", "Bulk messaging state"),
        ("app.state.template_state", "Template state"),
        ("app.state.message_state", "Message state"),
        ("app.state.analytics_state", "Analytics state"),
        ("app.state.flow_builder_state", "Flow builder state"),
        ("app.services.whatsapp_api", "WhatsApp API service"),
        ("app.services.webhook_service", "Webhook service"),
        ("app.pages.auth", "Auth pages"),
        ("app.pages.dashboard", "Dashboard pages"),
        ("app.pages.messages", "Message pages"),
        ("app.pages.templates", "Template pages"),
        ("app.pages.flows", "Flow pages"),
        ("app.pages.bot_testing", "Bot testing pages"),
        ("app.pages.bulk_messaging", "Bulk messaging pages"),
        ("app.pages.analytics", "Analytics pages"),
        ("app.main", "Main app"),
    ]
    
    passed = 0
    failed = 0
    
    for module, description in tests:
        if test_import(module, description):
            passed += 1
        else:
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All imports successful! Ready to run the app.")
        return True
    else:
        print("⚠️  Some imports failed. Please fix the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
