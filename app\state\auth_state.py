"""Authentication state management."""

import reflex as rx
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import bcrypt
import jwt
from sqlalchemy.orm import Session

from ..models.user import User, UserSession, UserRole, UserStatus
from ..database import get_session
from ..utils.security import create_access_token, verify_password, hash_password


class AuthState(rx.State):
    """Authentication state management."""
    
    # Current user state
    is_authenticated: bool = False
    current_user: Optional[Dict[str, Any]] = None
    access_token: Optional[str] = None
    
    # Login form state
    login_email: str = ""
    login_password: str = ""
    login_remember_me: bool = False
    login_error: str = ""
    login_loading: bool = False
    
    # Registration form state
    register_email: str = ""
    register_username: str = ""
    register_first_name: str = ""
    register_last_name: str = ""
    register_password: str = ""
    register_confirm_password: str = ""
    register_agree_terms: bool = False
    register_error: str = ""
    register_loading: bool = False
    register_success: bool = False
    
    # Password reset state
    reset_email: str = ""
    reset_error: str = ""
    reset_loading: bool = False
    reset_success: bool = False
    
    # Profile state
    profile_loading: bool = False
    profile_error: str = ""
    profile_success: str = ""
    
    def set_login_email(self, email: str):
        """Set login email."""
        self.login_email = email
        self.login_error = ""
    
    def set_login_password(self, password: str):
        """Set login password."""
        self.login_password = password
        self.login_error = ""
    
    def toggle_remember_me(self):
        """Toggle remember me checkbox."""
        self.login_remember_me = not self.login_remember_me
    
    async def login(self):
        """Authenticate user login."""
        if not self.login_email or not self.login_password:
            self.login_error = "Email and password are required"
            return
        
        self.login_loading = True
        self.login_error = ""
        
        try:
            with get_session() as session:
                # Find user by email
                user = session.query(User).filter(
                    User.email == self.login_email.lower(),
                    User.is_active == True
                ).first()
                
                if not user:
                    self.login_error = "Invalid email or password"
                    return
                
                # Check if account is locked
                if user.is_locked:
                    self.login_error = "Account is temporarily locked. Please try again later."
                    return
                
                # Check if account is active
                if user.status != UserStatus.ACTIVE:
                    if user.status == UserStatus.PENDING_VERIFICATION:
                        self.login_error = "Please verify your email address before logging in"
                    elif user.status == UserStatus.SUSPENDED:
                        self.login_error = "Your account has been suspended. Please contact support."
                    else:
                        self.login_error = "Account is not active"
                    return
                
                # Verify password
                if not verify_password(self.login_password, user.hashed_password):
                    # Increment failed login attempts
                    user.failed_login_attempts += 1
                    if user.failed_login_attempts >= 5:
                        user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                    session.commit()
                    
                    self.login_error = "Invalid email or password"
                    return
                
                # Reset failed login attempts on successful login
                user.failed_login_attempts = 0
                user.locked_until = None
                user.last_login = datetime.utcnow()
                
                # Create access token
                token_data = {
                    "user_id": user.id,
                    "email": user.email,
                    "role": user.role.value
                }
                access_token = create_access_token(token_data)
                
                # Create session record
                session_record = UserSession(
                    user_id=user.id,
                    session_token=access_token,
                    refresh_token=f"refresh_{access_token}",  # Simplified for demo
                    expires_at=datetime.utcnow() + timedelta(hours=24),
                    last_activity=datetime.utcnow(),
                    ip_address="127.0.0.1",  # Would get from request in real app
                    user_agent="Reflex App"  # Would get from request in real app
                )
                session.add(session_record)
                session.commit()
                
                # Set authentication state
                self.is_authenticated = True
                self.access_token = access_token
                self.current_user = {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "full_name": user.full_name,
                    "role": user.role.value,
                    "avatar_url": user.avatar_url,
                    "is_admin": user.is_admin,
                    "is_manager": user.is_manager
                }
                
                # Clear form
                self.login_email = ""
                self.login_password = ""
                self.login_remember_me = False
                
                # Redirect to dashboard
                return rx.redirect("/dashboard")
                
        except Exception as e:
            self.login_error = f"Login failed: {str(e)}"
        finally:
            self.login_loading = False
    
    def set_register_field(self, field: str, value: str):
        """Set registration form field."""
        setattr(self, f"register_{field}", value)
        self.register_error = ""
    
    def toggle_agree_terms(self):
        """Toggle agree to terms checkbox."""
        self.register_agree_terms = not self.register_agree_terms
    
    async def register(self):
        """Register new user."""
        # Validation
        if not all([
            self.register_email, self.register_username, 
            self.register_first_name, self.register_last_name,
            self.register_password, self.register_confirm_password
        ]):
            self.register_error = "All fields are required"
            return
        
        if self.register_password != self.register_confirm_password:
            self.register_error = "Passwords do not match"
            return
        
        if len(self.register_password) < 8:
            self.register_error = "Password must be at least 8 characters long"
            return
        
        if not self.register_agree_terms:
            self.register_error = "You must agree to the terms and conditions"
            return
        
        self.register_loading = True
        self.register_error = ""
        
        try:
            with get_session() as session:
                # Check if email already exists
                existing_email = session.query(User).filter(
                    User.email == self.register_email.lower()
                ).first()
                if existing_email:
                    self.register_error = "Email address is already registered"
                    return
                
                # Check if username already exists
                existing_username = session.query(User).filter(
                    User.username == self.register_username.lower()
                ).first()
                if existing_username:
                    self.register_error = "Username is already taken"
                    return
                
                # Create new user
                hashed_password = hash_password(self.register_password)
                new_user = User(
                    email=self.register_email.lower(),
                    username=self.register_username.lower(),
                    hashed_password=hashed_password,
                    first_name=self.register_first_name,
                    last_name=self.register_last_name,
                    role=UserRole.USER,
                    status=UserStatus.PENDING_VERIFICATION
                )
                
                session.add(new_user)
                session.commit()
                
                # Clear form and show success
                self.register_email = ""
                self.register_username = ""
                self.register_first_name = ""
                self.register_last_name = ""
                self.register_password = ""
                self.register_confirm_password = ""
                self.register_agree_terms = False
                self.register_success = True
                
        except Exception as e:
            self.register_error = f"Registration failed: {str(e)}"
        finally:
            self.register_loading = False
    
    def set_reset_email(self, email: str):
        """Set password reset email."""
        self.reset_email = email
        self.reset_error = ""
    
    async def request_password_reset(self):
        """Request password reset."""
        if not self.reset_email:
            self.reset_error = "Email address is required"
            return
        
        self.reset_loading = True
        self.reset_error = ""
        
        try:
            with get_session() as session:
                user = session.query(User).filter(
                    User.email == self.reset_email.lower(),
                    User.is_active == True
                ).first()
                
                # Always show success message for security
                # (don't reveal if email exists or not)
                self.reset_success = True
                self.reset_email = ""
                
                # In a real app, you would send an email here
                # if user exists
                
        except Exception as e:
            self.reset_error = f"Password reset failed: {str(e)}"
        finally:
            self.reset_loading = False
    
    async def logout(self):
        """Logout current user."""
        try:
            if self.access_token:
                with get_session() as session:
                    # Revoke session
                    session_record = session.query(UserSession).filter(
                        UserSession.session_token == self.access_token
                    ).first()
                    if session_record:
                        session_record.revoke("User logout")
                        session.commit()
            
            # Clear authentication state
            self.is_authenticated = False
            self.current_user = None
            self.access_token = None
            
            # Redirect to login
            return rx.redirect("/login")
            
        except Exception as e:
            print(f"Logout error: {e}")
            # Still clear state even if database update fails
            self.is_authenticated = False
            self.current_user = None
            self.access_token = None
            return rx.redirect("/login")
    
    def clear_errors(self):
        """Clear all error messages."""
        self.login_error = ""
        self.register_error = ""
        self.reset_error = ""
        self.profile_error = ""
    
    def clear_success_messages(self):
        """Clear all success messages."""
        self.register_success = False
        self.reset_success = False
        self.profile_success = ""

    @rx.var
    def is_admin(self) -> bool:
        """Check if current user is admin."""
        return self.current_user.get("is_admin", False) if self.current_user else False

    @rx.var
    def is_manager(self) -> bool:
        """Check if current user is manager."""
        return self.current_user.get("is_manager", False) if self.current_user else False
