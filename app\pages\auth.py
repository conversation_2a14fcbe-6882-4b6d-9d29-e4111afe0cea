"""Authentication pages - Login, Register, Password Reset."""

import reflex as rx
from ..state.auth_state import AuthState


def login_page() -> rx.Component:
    """Login page component."""
    return rx.container(
        rx.center(
            rx.card(
                rx.vstack(
                    # Logo and title
                    rx.center(
                        rx.vstack(
                            rx.icon("message-circle", size=48, color="blue"),
                            rx.heading("WhatsApp Business Platform", size="lg"),
                            rx.text("Sign in to your account", color="gray"),
                            spacing="2",
                            align="center"
                        )
                    ),
                    
                    # Login form
                    rx.form(
                        rx.vstack(
                            # Email field
                            rx.vstack(
                                rx.text("Email", size="sm", weight="medium"),
                                rx.input(
                                    placeholder="Enter your email",
                                    type="email",
                                    value=AuthState.login_email,
                                    on_change=AuthState.set_login_email,
                                    width="100%"
                                ),
                                spacing="1",
                                width="100%"
                            ),
                            
                            # Password field
                            rx.vstack(
                                rx.text("Password", size="sm", weight="medium"),
                                rx.input(
                                    placeholder="Enter your password",
                                    type="password",
                                    value=AuthState.login_password,
                                    on_change=AuthState.set_login_password,
                                    width="100%"
                                ),
                                spacing="1",
                                width="100%"
                            ),
                            
                            # Remember me and forgot password
                            rx.hstack(
                                rx.checkbox(
                                    "Remember me",
                                    checked=AuthState.login_remember_me,
                                    on_change=AuthState.toggle_remember_me
                                ),
                                rx.spacer(),
                                rx.link(
                                    "Forgot password?",
                                    href="/forgot-password",
                                    color="blue",
                                    size="sm"
                                ),
                                width="100%",
                                justify="between"
                            ),
                            
                            # Error message
                            rx.cond(
                                AuthState.login_error,
                                rx.callout(
                                    AuthState.login_error,
                                    icon="alert-circle",
                                    color_scheme="red",
                                    size="sm"
                                )
                            ),
                            
                            # Login button
                            rx.button(
                                rx.cond(
                                    AuthState.login_loading,
                                    rx.hstack(
                                        rx.spinner(size="sm"),
                                        rx.text("Signing in..."),
                                        spacing="2"
                                    ),
                                    rx.text("Sign In")
                                ),
                                on_click=AuthState.login,
                                loading=AuthState.login_loading,
                                width="100%",
                                size="lg"
                            ),
                            
                            spacing="4",
                            width="100%"
                        ),
                        width="100%"
                    ),
                    
                    # Divider
                    rx.divider(),
                    
                    # Register link
                    rx.center(
                        rx.hstack(
                            rx.text("Don't have an account?", size="sm"),
                            rx.link(
                                "Sign up",
                                href="/register",
                                color="blue",
                                size="sm",
                                weight="medium"
                            ),
                            spacing="2"
                        )
                    ),
                    
                    spacing="6",
                    width="100%"
                ),
                max_width="400px",
                width="100%",
                padding="8"
            ),
            min_height="100vh"
        ),
        max_width="100%",
        padding="4"
    )


def register_page() -> rx.Component:
    """Registration page component."""
    return rx.container(
        rx.center(
            rx.card(
                rx.vstack(
                    # Logo and title
                    rx.center(
                        rx.vstack(
                            rx.icon("message-circle", size=48, color="blue"),
                            rx.heading("Create Account", size="lg"),
                            rx.text("Join WhatsApp Business Platform", color="gray"),
                            spacing="2",
                            align="center"
                        )
                    ),
                    
                    # Success message
                    rx.cond(
                        AuthState.register_success,
                        rx.callout(
                            "Account created successfully! Please check your email to verify your account.",
                            icon="check-circle",
                            color_scheme="green",
                            size="sm"
                        )
                    ),
                    
                    # Registration form
                    rx.cond(
                        ~AuthState.register_success,
                        rx.form(
                            rx.vstack(
                                # Name fields
                                rx.hstack(
                                    rx.vstack(
                                        rx.text("First Name", size="sm", weight="medium"),
                                        rx.input(
                                            placeholder="First name",
                                            value=AuthState.register_first_name,
                                            on_change=lambda v: AuthState.set_register_field("first_name", v),
                                            width="100%"
                                        ),
                                        spacing="1",
                                        width="100%"
                                    ),
                                    rx.vstack(
                                        rx.text("Last Name", size="sm", weight="medium"),
                                        rx.input(
                                            placeholder="Last name",
                                            value=AuthState.register_last_name,
                                            on_change=lambda v: AuthState.set_register_field("last_name", v),
                                            width="100%"
                                        ),
                                        spacing="1",
                                        width="100%"
                                    ),
                                    spacing="3",
                                    width="100%"
                                ),
                                
                                # Email field
                                rx.vstack(
                                    rx.text("Email", size="sm", weight="medium"),
                                    rx.input(
                                        placeholder="Enter your email",
                                        type="email",
                                        value=AuthState.register_email,
                                        on_change=lambda v: AuthState.set_register_field("email", v),
                                        width="100%"
                                    ),
                                    spacing="1",
                                    width="100%"
                                ),
                                
                                # Username field
                                rx.vstack(
                                    rx.text("Username", size="sm", weight="medium"),
                                    rx.input(
                                        placeholder="Choose a username",
                                        value=AuthState.register_username,
                                        on_change=lambda v: AuthState.set_register_field("username", v),
                                        width="100%"
                                    ),
                                    spacing="1",
                                    width="100%"
                                ),
                                
                                # Password fields
                                rx.vstack(
                                    rx.text("Password", size="sm", weight="medium"),
                                    rx.input(
                                        placeholder="Create a password",
                                        type="password",
                                        value=AuthState.register_password,
                                        on_change=lambda v: AuthState.set_register_field("password", v),
                                        width="100%"
                                    ),
                                    spacing="1",
                                    width="100%"
                                ),
                                
                                rx.vstack(
                                    rx.text("Confirm Password", size="sm", weight="medium"),
                                    rx.input(
                                        placeholder="Confirm your password",
                                        type="password",
                                        value=AuthState.register_confirm_password,
                                        on_change=lambda v: AuthState.set_register_field("confirm_password", v),
                                        width="100%"
                                    ),
                                    spacing="1",
                                    width="100%"
                                ),
                                
                                # Terms checkbox
                                rx.checkbox(
                                    "I agree to the Terms of Service and Privacy Policy",
                                    checked=AuthState.register_agree_terms,
                                    on_change=AuthState.toggle_agree_terms,
                                    size="sm"
                                ),
                                
                                # Error message
                                rx.cond(
                                    AuthState.register_error,
                                    rx.callout(
                                        AuthState.register_error,
                                        icon="alert-circle",
                                        color_scheme="red",
                                        size="sm"
                                    )
                                ),
                                
                                # Register button
                                rx.button(
                                    rx.cond(
                                        AuthState.register_loading,
                                        rx.hstack(
                                            rx.spinner(size="sm"),
                                            rx.text("Creating account..."),
                                            spacing="2"
                                        ),
                                        rx.text("Create Account")
                                    ),
                                    on_click=AuthState.register,
                                    loading=AuthState.register_loading,
                                    width="100%",
                                    size="lg"
                                ),
                                
                                spacing="4",
                                width="100%"
                            ),
                            width="100%"
                        )
                    ),
                    
                    # Divider
                    rx.divider(),
                    
                    # Login link
                    rx.center(
                        rx.hstack(
                            rx.text("Already have an account?", size="sm"),
                            rx.link(
                                "Sign in",
                                href="/login",
                                color="blue",
                                size="sm",
                                weight="medium"
                            ),
                            spacing="2"
                        )
                    ),
                    
                    spacing="6",
                    width="100%"
                ),
                max_width="450px",
                width="100%",
                padding="8"
            ),
            min_height="100vh"
        ),
        max_width="100%",
        padding="4"
    )


def forgot_password_page() -> rx.Component:
    """Forgot password page component."""
    return rx.container(
        rx.center(
            rx.card(
                rx.vstack(
                    # Logo and title
                    rx.center(
                        rx.vstack(
                            rx.icon("message-circle", size=48, color="blue"),
                            rx.heading("Reset Password", size="lg"),
                            rx.text("Enter your email to reset your password", color="gray"),
                            spacing="2",
                            align="center"
                        )
                    ),
                    
                    # Success message
                    rx.cond(
                        AuthState.reset_success,
                        rx.callout(
                            "Password reset instructions have been sent to your email.",
                            icon="check-circle",
                            color_scheme="green",
                            size="sm"
                        )
                    ),
                    
                    # Reset form
                    rx.cond(
                        ~AuthState.reset_success,
                        rx.form(
                            rx.vstack(
                                # Email field
                                rx.vstack(
                                    rx.text("Email", size="sm", weight="medium"),
                                    rx.input(
                                        placeholder="Enter your email",
                                        type="email",
                                        value=AuthState.reset_email,
                                        on_change=AuthState.set_reset_email,
                                        width="100%"
                                    ),
                                    spacing="1",
                                    width="100%"
                                ),
                                
                                # Error message
                                rx.cond(
                                    AuthState.reset_error,
                                    rx.callout(
                                        AuthState.reset_error,
                                        icon="alert-circle",
                                        color_scheme="red",
                                        size="sm"
                                    )
                                ),
                                
                                # Reset button
                                rx.button(
                                    rx.cond(
                                        AuthState.reset_loading,
                                        rx.hstack(
                                            rx.spinner(size="sm"),
                                            rx.text("Sending..."),
                                            spacing="2"
                                        ),
                                        rx.text("Send Reset Instructions")
                                    ),
                                    on_click=AuthState.request_password_reset,
                                    loading=AuthState.reset_loading,
                                    width="100%",
                                    size="lg"
                                ),
                                
                                spacing="4",
                                width="100%"
                            ),
                            width="100%"
                        )
                    ),
                    
                    # Divider
                    rx.divider(),
                    
                    # Back to login link
                    rx.center(
                        rx.link(
                            "← Back to Sign In",
                            href="/login",
                            color="blue",
                            size="sm"
                        )
                    ),
                    
                    spacing="6",
                    width="100%"
                ),
                max_width="400px",
                width="100%",
                padding="8"
            ),
            min_height="100vh"
        ),
        max_width="100%",
        padding="4"
    )
