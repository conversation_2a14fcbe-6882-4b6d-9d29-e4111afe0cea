"""Bot testing state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
import json
import asyncio
from datetime import datetime

from ..models.bot import BotConfiguration, BotTest, BotTestResult
from ..database import get_session
from .auth_state import AuthState


class BotTestingState(AuthState):
    """Bot testing state management."""
    
    # Bot configuration management
    bot_configs: List[Dict[str, Any]] = []
    current_bot_config: Optional[Dict[str, Any]] = None
    selected_config_id: Optional[int] = None
    
    # Bot test form
    test_phone_number: str = ""
    test_message: str = ""
    test_scenario_name: str = ""
    
    # Test execution
    is_testing: bool = False
    test_progress: int = 0
    test_logs: List[str] = []
    current_test_id: Optional[int] = None
    
    # Test results
    test_results: List[Dict[str, Any]] = []
    test_statistics: Dict[str, Any] = {}
    
    # JSON config upload
    config_json: str = ""
    config_upload_error: str = ""
    config_upload_success: str = ""
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    
    # Bot configuration creation
    new_config_name: str = ""
    new_config_type: str = "customer_service"
    new_config_description: str = ""
    
    async def load_bot_configurations(self):
        """Load user's bot configurations."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                configs = session.query(BotConfiguration).filter(
                    BotConfiguration.user_id == self.current_user["id"],
                    BotConfiguration.is_active == True
                ).all()
                
                self.bot_configs = [
                    {
                        "id": config.id,
                        "bot_name": config.bot_name,
                        "bot_type": config.bot_type,
                        "description": config.description,
                        "test_scenarios": config.test_scenarios,
                        "expected_responses": config.expected_responses,
                        "test_settings": config.test_settings,
                        "created_at": config.created_at.isoformat() if config.created_at else None,
                        "updated_at": config.updated_at.isoformat() if config.updated_at else None
                    }
                    for config in configs
                ]
                
        except Exception as e:
            self.error_message = f"Failed to load bot configurations: {str(e)}"
        finally:
            self.loading = False
    
    def select_bot_config(self, config_id: int):
        """Select a bot configuration for testing."""
        self.selected_config_id = config_id
        self.current_bot_config = next(
            (config for config in self.bot_configs if config["id"] == config_id),
            None
        )
        self.error_message = ""
        self.success_message = f"Selected bot configuration: {self.current_bot_config['bot_name']}"
    
    async def create_bot_config_from_json(self):
        """Create a new bot configuration from JSON."""
        if not self.config_json.strip():
            self.config_upload_error = "Please provide JSON configuration"
            return
        
        try:
            config_data = json.loads(self.config_json)
            
            # Validate required fields
            required_fields = ["bot_name", "bot_type", "test_scenarios"]
            for field in required_fields:
                if field not in config_data:
                    self.config_upload_error = f"Missing required field: {field}"
                    return
            
            # Create new configuration
            with get_session() as session:
                new_config = BotConfiguration(
                    user_id=self.current_user["id"],
                    bot_name=config_data["bot_name"],
                    bot_type=config_data["bot_type"],
                    description=config_data.get("description", ""),
                    test_scenarios=config_data["test_scenarios"],
                    expected_responses=config_data.get("expected_responses", {}),
                    test_settings=config_data.get("test_settings", {})
                )
                
                session.add(new_config)
                session.commit()
                session.refresh(new_config)
                
                self.config_upload_success = f"Bot configuration '{config_data['bot_name']}' created successfully"
                self.config_json = ""
                self.config_upload_error = ""
                
                # Reload configurations
                await self.load_bot_configurations()
                
        except json.JSONDecodeError:
            self.config_upload_error = "Invalid JSON format"
        except Exception as e:
            self.config_upload_error = f"Failed to create configuration: {str(e)}"
    
    async def run_smart_bot_test(self):
        """Run intelligent bot test using selected configuration."""
        if not self.current_bot_config:
            self.error_message = "Please select a bot configuration first"
            return
        
        if not self.test_phone_number.strip():
            self.error_message = "Please enter a phone number to test"
            return
        
        self.is_testing = True
        self.test_progress = 0
        self.test_logs = []
        self.error_message = ""
        
        try:
            # Create test record
            with get_session() as session:
                test_record = BotTest(
                    configuration_id=self.current_bot_config["id"],
                    test_name=f"Smart Test - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    test_type="smart_json",
                    target_phone=self.test_phone_number,
                    test_parameters={
                        "scenarios": self.current_bot_config["test_scenarios"],
                        "settings": self.current_bot_config.get("test_settings", {})
                    }
                )
                
                session.add(test_record)
                session.commit()
                session.refresh(test_record)
                self.current_test_id = test_record.id
            
            # Simulate test execution
            scenarios = self.current_bot_config["test_scenarios"]
            total_messages = sum(len(scenario.get("messages", [])) for scenario in scenarios)
            
            self.test_logs.append(f"🧠 Starting smart bot test for {self.current_bot_config['bot_name']}")
            self.test_logs.append(f"📱 Target: {self.test_phone_number}")
            self.test_logs.append(f"📊 Total scenarios: {len(scenarios)}")
            self.test_logs.append(f"💬 Total messages: {total_messages}")
            
            message_count = 0
            for scenario in scenarios:
                self.test_logs.append(f"\n🎯 Testing scenario: {scenario['name']}")
                
                for message in scenario.get("messages", []):
                    message_count += 1
                    self.test_progress = int((message_count / total_messages) * 100)
                    
                    self.test_logs.append(f"📤 Sending: {message['text']}")
                    
                    # Simulate API call delay
                    await asyncio.sleep(1)
                    
                    # Simulate response
                    self.test_logs.append(f"📥 Bot response received")
                    
                    if message_count % 3 == 0:  # Simulate some analysis
                        self.test_logs.append(f"✅ Response contains expected keywords")
            
            # Complete test
            self.test_progress = 100
            self.test_logs.append(f"\n🎉 Smart bot test completed successfully!")
            self.test_logs.append(f"📈 Test results saved to database")
            
            # Save test results
            with get_session() as session:
                test_record = session.get(BotTest, self.current_test_id)
                if test_record:
                    test_record.status = "completed"
                    test_record.completed_at = datetime.utcnow()
                    
                    # Create test result
                    result = BotTestResult(
                        test_id=test_record.id,
                        scenario_name="Overall Test",
                        messages_sent=total_messages,
                        responses_received=total_messages,
                        success_rate=95.0,  # Simulated
                        average_response_time=1.2,  # Simulated
                        test_data={
                            "scenarios_tested": len(scenarios),
                            "total_messages": total_messages,
                            "logs": self.test_logs
                        }
                    )
                    
                    session.add(result)
                    session.commit()
            
            self.success_message = "Bot test completed successfully!"
            
        except Exception as e:
            self.error_message = f"Test failed: {str(e)}"
            self.test_logs.append(f"❌ Error: {str(e)}")
        finally:
            self.is_testing = False
    
    async def load_test_results(self):
        """Load recent test results."""
        if not self.is_authenticated:
            return
        
        try:
            with get_session() as session:
                # Get tests for user's configurations
                config_ids = [config["id"] for config in self.bot_configs]
                
                tests = session.query(BotTest).filter(
                    BotTest.configuration_id.in_(config_ids)
                ).order_by(BotTest.created_at.desc()).limit(10).all()
                
                self.test_results = [
                    {
                        "id": test.id,
                        "test_name": test.test_name,
                        "test_type": test.test_type,
                        "status": test.status,
                        "target_phone": test.target_phone,
                        "created_at": test.created_at.isoformat() if test.created_at else None,
                        "completed_at": test.completed_at.isoformat() if test.completed_at else None,
                        "config_name": next(
                            (config["bot_name"] for config in self.bot_configs 
                             if config["id"] == test.configuration_id), 
                            "Unknown"
                        )
                    }
                    for test in tests
                ]
                
        except Exception as e:
            self.error_message = f"Failed to load test results: {str(e)}"
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
        self.config_upload_error = ""
        self.config_upload_success = ""
    
    def set_test_phone_number(self, phone: str):
        """Set test phone number."""
        self.test_phone_number = phone
        self.clear_messages()
    
    def set_config_json(self, json_content: str):
        """Set configuration JSON content."""
        self.config_json = json_content
        self.config_upload_error = ""
        self.config_upload_success = ""
