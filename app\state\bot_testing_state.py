"""Bot testing state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
import json
import asyncio
from datetime import datetime

from ..models.bot import BotConfiguration, BotTest, BotTestResult
from ..models.whatsapp import WhatsAppAccount
from ..database import get_session
from ..services.whatsapp_api import WhatsAppAPIService, WhatsAppAPIError
from .auth_state import AuthState


class BotTestingState(AuthState):
    """Bot testing state management."""
    
    # Bot configuration management
    bot_configs: List[Dict[str, Any]] = []
    current_bot_config: Optional[Dict[str, Any]] = None
    selected_config_id: Optional[int] = None
    
    # Bot test form
    test_phone_number: str = ""
    test_message: str = ""
    test_scenario_name: str = ""
    
    # Test execution
    is_testing: bool = False
    test_progress: int = 0
    test_logs: List[str] = []
    current_test_id: Optional[int] = None
    
    # Test results
    test_results: List[Dict[str, Any]] = []
    test_statistics: Dict[str, Any] = {}
    
    # JSON config upload
    config_json: str = ""
    config_upload_error: str = ""
    config_upload_success: str = ""
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_create_form: bool = False
    
    # Bot configuration creation
    new_config_name: str = ""
    new_config_type: str = "customer_service"
    new_config_description: str = ""
    
    async def load_bot_configurations(self):
        """Load user's bot configurations."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                configs = session.query(BotConfiguration).filter(
                    BotConfiguration.user_id == self.current_user["id"],
                    BotConfiguration.is_active == True
                ).all()
                
                self.bot_configs = [
                    {
                        "id": config.id,
                        "bot_name": config.bot_name,
                        "bot_type": config.bot_type,
                        "description": config.description,
                        "test_scenarios": config.test_scenarios,
                        "expected_responses": config.expected_responses,
                        "test_settings": config.test_settings,
                        "created_at": config.created_at.isoformat() if config.created_at else None,
                        "updated_at": config.updated_at.isoformat() if config.updated_at else None
                    }
                    for config in configs
                ]
                
        except Exception as e:
            self.error_message = f"Failed to load bot configurations: {str(e)}"
        finally:
            self.loading = False
    
    def select_bot_config(self, config_id: int):
        """Select a bot configuration for testing."""
        self.selected_config_id = config_id
        self.current_bot_config = next(
            (config for config in self.bot_configs if config["id"] == config_id),
            None
        )
        self.error_message = ""
        self.success_message = f"Selected bot configuration: {self.current_bot_config['bot_name']}"
    
    async def create_bot_config_from_json(self):
        """Create a new bot configuration from JSON."""
        if not self.config_json.strip():
            self.config_upload_error = "Please provide JSON configuration"
            return
        
        try:
            config_data = json.loads(self.config_json)
            
            # Validate required fields
            required_fields = ["bot_name", "bot_type", "test_scenarios"]
            for field in required_fields:
                if field not in config_data:
                    self.config_upload_error = f"Missing required field: {field}"
                    return
            
            # Create new configuration
            with get_session() as session:
                new_config = BotConfiguration(
                    user_id=self.current_user["id"],
                    bot_name=config_data["bot_name"],
                    bot_type=config_data["bot_type"],
                    description=config_data.get("description", ""),
                    test_scenarios=config_data["test_scenarios"],
                    expected_responses=config_data.get("expected_responses", {}),
                    test_settings=config_data.get("test_settings", {})
                )
                
                session.add(new_config)
                session.commit()
                session.refresh(new_config)
                
                self.config_upload_success = f"Bot configuration '{config_data['bot_name']}' created successfully"
                self.config_json = ""
                self.config_upload_error = ""
                
                # Reload configurations
                await self.load_bot_configurations()
                
        except json.JSONDecodeError:
            self.config_upload_error = "Invalid JSON format"
        except Exception as e:
            self.config_upload_error = f"Failed to create configuration: {str(e)}"
    
    async def run_smart_bot_test(self):
        """Run intelligent bot test using selected configuration with real WhatsApp API."""
        if not self.current_bot_config:
            self.error_message = "Please select a bot configuration first"
            return

        if not self.test_phone_number.strip():
            self.error_message = "Please enter a phone number to test"
            return

        # Validate phone number format
        phone = self.test_phone_number.strip()
        if not phone.startswith('+'):
            phone = '+' + phone

        self.is_testing = True
        self.test_progress = 0
        self.test_logs = []
        self.error_message = ""

        try:
            # Create test record
            with get_session() as session:
                test_record = BotTest(
                    configuration_id=self.current_bot_config["id"],
                    user_id=self.current_user["id"],
                    test_name=f"Smart Test - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    test_type="smart_json",
                    target_phone=phone,
                    started_at=datetime.utcnow()
                )

                session.add(test_record)
                session.commit()
                session.refresh(test_record)
                self.current_test_id = test_record.id

            # Execute test scenarios
            scenarios = self.current_bot_config["test_scenarios"]
            test_settings = self.current_bot_config.get("test_settings", {})

            total_messages = sum(len(scenario.get("messages", [])) for scenario in scenarios)

            self.test_logs.append(f"🧠 Starting smart bot test for {self.current_bot_config['bot_name']}")
            self.test_logs.append(f"📱 Target: {phone}")
            self.test_logs.append(f"📊 Total scenarios: {len(scenarios)}")
            self.test_logs.append(f"💬 Total messages: {total_messages}")
            self.test_logs.append(f"⚙️ Settings: {test_settings}")

            # Test execution statistics
            test_stats = {
                "scenarios_tested": 0,
                "scenarios_passed": 0,
                "total_messages": 0,
                "successful_sends": 0,
                "bot_responses_detected": 0,
                "flows_completed": 0,
                "start_time": datetime.utcnow()
            }

            message_count = 0
            scenario_results = []

            for scenario_idx, scenario in enumerate(scenarios):
                scenario_name = scenario["name"]
                scenario_messages = scenario.get("messages", [])
                expected_keywords = scenario.get("expected_keywords", [])

                self.test_logs.append(f"\n🎯 Testing scenario {scenario_idx + 1}/{len(scenarios)}: {scenario_name}")
                self.test_logs.append(f"📝 Description: {scenario.get('description', 'No description')}")
                self.test_logs.append(f"🔍 Expected keywords: {', '.join(expected_keywords)}")

                scenario_result = await self._execute_scenario(
                    phone, scenario, message_count, total_messages, test_settings
                )

                scenario_results.append(scenario_result)
                message_count += len(scenario_messages)

                # Update statistics
                test_stats["scenarios_tested"] += 1
                test_stats["total_messages"] += scenario_result["messages_sent"]
                test_stats["successful_sends"] += scenario_result["successful_sends"]

                if scenario_result["scenario_passed"]:
                    test_stats["scenarios_passed"] += 1
                    self.test_logs.append(f"✅ Scenario '{scenario_name}' PASSED")
                else:
                    self.test_logs.append(f"❌ Scenario '{scenario_name}' FAILED")

                if scenario_result["bot_responded"]:
                    test_stats["bot_responses_detected"] += 1

                if scenario_result["flow_completed"]:
                    test_stats["flows_completed"] += 1

                # Wait between scenarios
                scenario_delay = test_settings.get("scenario_delay", 3)
                if scenario_idx < len(scenarios) - 1:  # Don't wait after last scenario
                    self.test_logs.append(f"⏳ Waiting {scenario_delay}s before next scenario...")
                    await asyncio.sleep(scenario_delay)

            # Complete test
            self.test_progress = 100
            test_stats["end_time"] = datetime.utcnow()
            test_duration = (test_stats["end_time"] - test_stats["start_time"]).total_seconds()

            success_rate = (test_stats["scenarios_passed"] / test_stats["scenarios_tested"]) * 100 if test_stats["scenarios_tested"] > 0 else 0

            self.test_logs.append(f"\n🎉 Smart bot test completed!")
            self.test_logs.append(f"📊 Test Summary:")
            self.test_logs.append(f"   • Duration: {test_duration:.1f} seconds")
            self.test_logs.append(f"   • Scenarios: {test_stats['scenarios_passed']}/{test_stats['scenarios_tested']} passed ({success_rate:.1f}%)")
            self.test_logs.append(f"   • Messages: {test_stats['successful_sends']}/{test_stats['total_messages']} sent successfully")
            self.test_logs.append(f"   • Bot responses detected: {test_stats['bot_responses_detected']}")
            self.test_logs.append(f"   • Conversation flows completed: {test_stats['flows_completed']}")

            # Save comprehensive test results
            await self._save_test_results(test_stats, scenario_results, test_duration)

            self.success_message = f"Bot test completed! {test_stats['scenarios_passed']}/{test_stats['scenarios_tested']} scenarios passed"

        except Exception as e:
            self.error_message = f"Test failed: {str(e)}"
            self.test_logs.append(f"❌ Test failed: {str(e)}")

            # Mark test as failed in database
            if self.current_test_id:
                with get_session() as session:
                    test_record = session.get(BotTest, self.current_test_id)
                    if test_record:
                        test_record.status = "FAILED"
                        test_record.error_message = str(e)
                        test_record.completed_at = datetime.utcnow()
                        session.commit()
        finally:
            self.is_testing = False
    
    async def load_test_results(self):
        """Load recent test results."""
        if not self.is_authenticated:
            return
        
        try:
            with get_session() as session:
                # Get tests for user's configurations
                config_ids = [config["id"] for config in self.bot_configs]
                
                tests = session.query(BotTest).filter(
                    BotTest.configuration_id.in_(config_ids)
                ).order_by(BotTest.created_at.desc()).limit(10).all()
                
                self.test_results = [
                    {
                        "id": test.id,
                        "test_name": test.test_name,
                        "test_type": test.test_type,
                        "status": test.status,
                        "target_phone": test.target_phone,
                        "created_at": test.created_at.isoformat() if test.created_at else None,
                        "completed_at": test.completed_at.isoformat() if test.completed_at else None,
                        "config_name": next(
                            (config["bot_name"] for config in self.bot_configs 
                             if config["id"] == test.configuration_id), 
                            "Unknown"
                        )
                    }
                    for test in tests
                ]
                
        except Exception as e:
            self.error_message = f"Failed to load test results: {str(e)}"
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
        self.config_upload_error = ""
        self.config_upload_success = ""
    
    def set_test_phone_number(self, phone: str):
        """Set test phone number."""
        self.test_phone_number = phone
        self.clear_messages()
    
    def set_config_json(self, json_content: str):
        """Set configuration JSON content."""
        self.config_json = json_content
        self.config_upload_error = ""
        self.config_upload_success = ""

    async def _execute_scenario(self, phone: str, scenario: dict, message_offset: int, total_messages: int, test_settings: dict) -> dict:
        """Execute a single test scenario."""
        scenario_name = scenario["name"]
        messages = scenario.get("messages", [])
        expected_keywords = scenario.get("expected_keywords", [])

        scenario_result = {
            "scenario_name": scenario_name,
            "scenario_passed": False,
            "bot_responded": False,
            "flow_completed": False,
            "messages_sent": 0,
            "successful_sends": 0,
            "response_time": 0,
            "keyword_matches": 0,
            "errors": []
        }

        try:
            for msg_idx, message in enumerate(messages):
                message_text = message["text"]
                wait_time = message.get("wait_time", test_settings.get("default_wait_time", 2))
                description = message.get("description", "")

                # Update progress
                current_msg = message_offset + msg_idx + 1
                self.test_progress = int((current_msg / total_messages) * 100)

                self.test_logs.append(f"📤 [{msg_idx + 1}/{len(messages)}] Sending: '{message_text}'")
                if description:
                    self.test_logs.append(f"   💡 {description}")

                # Simulate sending message (replace with real API call)
                start_time = datetime.utcnow()
                success = await self._send_test_message(phone, message_text)
                end_time = datetime.utcnow()

                response_time = (end_time - start_time).total_seconds()
                scenario_result["messages_sent"] += 1

                if success:
                    scenario_result["successful_sends"] += 1
                    scenario_result["response_time"] += response_time
                    self.test_logs.append(f"   ✅ Message sent successfully ({response_time:.2f}s)")

                    # Wait for bot response
                    self.test_logs.append(f"   ⏳ Waiting {wait_time}s for bot response...")
                    await asyncio.sleep(wait_time)

                    # Simulate bot response analysis (replace with real response checking)
                    bot_response = await self._check_bot_response(phone, expected_keywords)
                    if bot_response["responded"]:
                        scenario_result["bot_responded"] = True
                        scenario_result["keyword_matches"] += bot_response["keyword_matches"]
                        self.test_logs.append(f"   📥 Bot response detected: {bot_response['response_preview']}")
                        if bot_response["keyword_matches"] > 0:
                            self.test_logs.append(f"   🎯 Keywords matched: {bot_response['matched_keywords']}")
                    else:
                        self.test_logs.append(f"   ⚠️ No bot response detected")
                else:
                    error_msg = f"Failed to send message: {message_text}"
                    scenario_result["errors"].append(error_msg)
                    self.test_logs.append(f"   ❌ {error_msg}")

            # Determine if scenario passed
            if scenario_result["successful_sends"] > 0:
                if scenario_result["bot_responded"]:
                    if expected_keywords and scenario_result["keyword_matches"] > 0:
                        scenario_result["scenario_passed"] = True
                        scenario_result["flow_completed"] = True
                    elif not expected_keywords:  # No specific keywords expected
                        scenario_result["scenario_passed"] = True
                        scenario_result["flow_completed"] = True

            # Calculate average response time
            if scenario_result["successful_sends"] > 0:
                scenario_result["response_time"] = scenario_result["response_time"] / scenario_result["successful_sends"]

        except Exception as e:
            error_msg = f"Scenario execution failed: {str(e)}"
            scenario_result["errors"].append(error_msg)
            self.test_logs.append(f"❌ {error_msg}")

        return scenario_result

    async def _send_test_message(self, phone: str, message: str) -> bool:
        """Send a test message via WhatsApp API."""
        try:
            # Get user's WhatsApp account
            with get_session() as session:
                account = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).first()

                if not account:
                    self.test_logs.append(f"⚠️ No active WhatsApp account found - using simulation mode")
                    # Fallback to simulation
                    await asyncio.sleep(0.5)
                    import random
                    return random.random() > 0.05

                # Use real WhatsApp API
                api_service = WhatsAppAPIService(account)
                result = api_service.send_text_message(phone, message)

                if "messages" in result and len(result["messages"]) > 0:
                    message_id = result["messages"][0].get("id")
                    self.test_logs.append(f"   📧 Message ID: {message_id}")
                    return True
                else:
                    error_msg = result.get("error", {}).get("message", "Unknown API error")
                    self.test_logs.append(f"   ❌ API Error: {error_msg}")
                    return False

        except WhatsAppAPIError as e:
            self.test_logs.append(f"❌ WhatsApp API Error: {str(e)}")
            return False
        except Exception as e:
            self.test_logs.append(f"❌ Unexpected Error: {str(e)}")
            return False

    async def _check_bot_response(self, phone: str, expected_keywords: list) -> dict:
        """Check for bot response and analyze keywords."""
        try:
            # TODO: Implement real response checking
            # For now, simulate response analysis
            await asyncio.sleep(0.3)  # Simulate response check delay

            # Simulate bot response
            import random
            responded = random.random() > 0.2  # 80% chance of response

            if responded:
                # Simulate response content
                sample_responses = [
                    "Hello! How can I help you today?",
                    "Welcome to our service. Please select an option:",
                    "Thank you for contacting us. Here are your options:",
                    "I'm here to assist you. What would you like to know?",
                    "Please choose from the following menu items:"
                ]

                response_text = random.choice(sample_responses)

                # Check for keyword matches
                matched_keywords = []
                if expected_keywords:
                    for keyword in expected_keywords:
                        if keyword.lower() in response_text.lower():
                            matched_keywords.append(keyword)

                return {
                    "responded": True,
                    "response_preview": response_text[:50] + "..." if len(response_text) > 50 else response_text,
                    "keyword_matches": len(matched_keywords),
                    "matched_keywords": matched_keywords,
                    "full_response": response_text
                }
            else:
                return {
                    "responded": False,
                    "response_preview": "",
                    "keyword_matches": 0,
                    "matched_keywords": [],
                    "full_response": ""
                }

        except Exception as e:
            self.test_logs.append(f"❌ Response check error: {str(e)}")
            return {
                "responded": False,
                "response_preview": "",
                "keyword_matches": 0,
                "matched_keywords": [],
                "full_response": ""
            }

    async def _save_test_results(self, test_stats: dict, scenario_results: list, test_duration: float):
        """Save comprehensive test results to database."""
        try:
            with get_session() as session:
                # Update main test record
                test_record = session.get(BotTest, self.current_test_id)
                if test_record:
                    test_record.status = "COMPLETED"
                    test_record.completed_at = datetime.utcnow()
                    test_record.total_scenarios = test_stats["scenarios_tested"]
                    test_record.passed_scenarios = test_stats["scenarios_passed"]
                    test_record.failed_scenarios = test_stats["scenarios_tested"] - test_stats["scenarios_passed"]
                    test_record.total_messages_sent = test_stats["total_messages"]
                    test_record.average_response_time = sum(r["response_time"] for r in scenario_results) / len(scenario_results) if scenario_results else 0

                    # Save individual scenario results
                    for idx, scenario_result in enumerate(scenario_results):
                        result_record = BotTestResult(
                            test_id=test_record.id,
                            scenario_name=scenario_result["scenario_name"],
                            scenario_order=idx + 1,
                            started_at=test_stats["start_time"],
                            completed_at=test_stats["end_time"],
                            test_message=f"Scenario: {scenario_result['scenario_name']}",
                            expected_response=f"Expected keywords: {', '.join(scenario_result.get('expected_keywords', []))}",
                            actual_response=f"Bot responded: {scenario_result['bot_responded']}, Keywords matched: {scenario_result['keyword_matches']}",
                            status="PASSED" if scenario_result["scenario_passed"] else "FAILED",
                            response_time=scenario_result["response_time"],
                            test_data={
                                "messages_sent": scenario_result["messages_sent"],
                                "successful_sends": scenario_result["successful_sends"],
                                "bot_responded": scenario_result["bot_responded"],
                                "flow_completed": scenario_result["flow_completed"],
                                "keyword_matches": scenario_result["keyword_matches"],
                                "errors": scenario_result["errors"]
                            }
                        )
                        session.add(result_record)

                    session.commit()
                    self.test_logs.append(f"💾 Test results saved to database")

        except Exception as e:
            self.test_logs.append(f"❌ Failed to save test results: {str(e)}")

    async def load_sample_bot_config(self):
        """Load the sample bot configuration for testing."""
        try:
            sample_config = {
                "bot_name": "Sample Customer Service Bot",
                "bot_type": "customer_service",
                "description": "Sample configuration for testing customer service bot flows",
                "test_scenarios": [
                    {
                        "name": "Welcome Flow",
                        "description": "Test bot welcome and initial responses",
                        "expected_keywords": ["welcome", "hello", "menu", "help", "options"],
                        "messages": [
                            {
                                "text": "hi",
                                "description": "Test welcome trigger",
                                "wait_time": 2
                            },
                            {
                                "text": "hello",
                                "description": "Test welcome variant",
                                "wait_time": 2
                            }
                        ]
                    },
                    {
                        "name": "Support Request",
                        "description": "Test customer support flow",
                        "expected_keywords": ["support", "agent", "help", "contact"],
                        "messages": [
                            {
                                "text": "I need help",
                                "description": "Request assistance",
                                "wait_time": 3
                            },
                            {
                                "text": "talk to agent",
                                "description": "Request human agent",
                                "wait_time": 3
                            }
                        ]
                    }
                ],
                "test_settings": {
                    "default_wait_time": 2,
                    "response_timeout": 10,
                    "scenario_delay": 3,
                    "max_conversation_depth": 20,
                    "retry_failed_messages": True,
                    "log_detailed_responses": True
                }
            }

            self.config_json = json.dumps(sample_config, indent=2)
            self.config_upload_success = "Sample configuration loaded"
            self.config_upload_error = ""

        except Exception as e:
            self.config_upload_error = f"Failed to load sample config: {str(e)}"

    def toggle_create_form(self):
        """Toggle the create form visibility."""
        self.show_create_form = not self.show_create_form
        self.clear_messages()
