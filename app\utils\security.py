"""Security utilities for authentication and authorization."""

import bcrypt
import jwt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from passlib.context import Crypt<PERSON>ontext

from rxconfig import SECURITY_CONFIG

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = SECURITY_CONFIG["secret_key"]
ALGORITHM = SECURITY_CONFIG["algorithm"]
ACCESS_TOKEN_EXPIRE_MINUTES = SECURITY_CONFIG["access_token_expire_minutes"]


def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def verify_access_token(token: str) -> Optional[Dict[str, Any]]:
    """Verify and decode a JWT access token."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None


def create_refresh_token(user_id: int) -> str:
    """Create a refresh token for long-term authentication."""
    data = {
        "user_id": user_id,
        "type": "refresh",
        "exp": datetime.utcnow() + timedelta(days=SECURITY_CONFIG["refresh_token_expire_days"])
    }
    return jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)


def verify_refresh_token(token: str) -> Optional[int]:
    """Verify a refresh token and return user ID."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("type") != "refresh":
            return None
        return payload.get("user_id")
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None


def generate_password_reset_token(email: str) -> str:
    """Generate a password reset token."""
    data = {
        "email": email,
        "type": "password_reset",
        "exp": datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry
    }
    return jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)


def verify_password_reset_token(token: str) -> Optional[str]:
    """Verify a password reset token and return email."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("type") != "password_reset":
            return None
        return payload.get("email")
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None


def generate_email_verification_token(email: str) -> str:
    """Generate an email verification token."""
    data = {
        "email": email,
        "type": "email_verification",
        "exp": datetime.utcnow() + timedelta(days=7)  # 7 days expiry
    }
    return jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)


def verify_email_verification_token(token: str) -> Optional[str]:
    """Verify an email verification token and return email."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("type") != "email_verification":
            return None
        return payload.get("email")
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None


def is_strong_password(password: str) -> tuple[bool, str]:
    """Check if password meets security requirements."""
    if len(password) < SECURITY_CONFIG["password_min_length"]:
        return False, f"Password must be at least {SECURITY_CONFIG['password_min_length']} characters long"
    
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    if not has_upper:
        return False, "Password must contain at least one uppercase letter"
    
    if not has_lower:
        return False, "Password must contain at least one lowercase letter"
    
    if not has_digit:
        return False, "Password must contain at least one number"
    
    if not has_special:
        return False, "Password must contain at least one special character"
    
    return True, "Password is strong"


def sanitize_input(input_string: str) -> str:
    """Sanitize user input to prevent XSS and other attacks."""
    if not input_string:
        return ""
    
    # Basic HTML escaping
    input_string = input_string.replace("&", "&amp;")
    input_string = input_string.replace("<", "&lt;")
    input_string = input_string.replace(">", "&gt;")
    input_string = input_string.replace('"', "&quot;")
    input_string = input_string.replace("'", "&#x27;")
    
    return input_string.strip()


def validate_email(email: str) -> bool:
    """Validate email format."""
    import re
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_phone_number(phone: str) -> bool:
    """Validate phone number format."""
    import re
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if it's a valid length (7-15 digits)
    return 7 <= len(digits_only) <= 15


def generate_api_key() -> str:
    """Generate a secure API key."""
    import secrets
    import string
    
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def generate_webhook_secret() -> str:
    """Generate a webhook secret for verification."""
    import secrets
    
    return secrets.token_urlsafe(32)
