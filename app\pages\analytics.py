"""Analytics and reporting pages."""

import reflex as rx
from ..components.layout import protected_layout, page_header
from ..state.analytics_state import AnalyticsState


def metric_card(title: str, value: str, subtitle: str = "", icon: str = "bar-chart", color: str = "blue") -> rx.Component:
    """Metric card component."""
    return rx.card(
        rx.vstack(
            rx.hstack(
                rx.icon(icon, size=24, color=color),
                rx.spacer(),
                rx.badge("Live", color_scheme="green", size="sm"),
                width="100%",
                align="center"
            ),
            rx.vstack(
                rx.heading(value, size="xl", color=color),
                rx.text(title, weight="bold"),
                rx.cond(
                    subtitle != "",
                    rx.text(subtitle, size="sm", color="gray")
                ),
                spacing="1",
                align="start",
                width="100%"
            ),
            spacing="3",
            width="100%"
        ),
        width="100%",
        height="120px"
    )


def chart_card(title: str, chart_component: rx.Component) -> rx.Component:
    """Chart card wrapper."""
    return rx.card(
        rx.vstack(
            rx.heading(title, size="md"),
            chart_component,
            spacing="4",
            width="100%"
        ),
        width="100%",
        height="400px"
    )


def top_performers_card(title: str, items: list, value_key: str, label_key: str) -> rx.Component:
    """Top performers card component."""
    return rx.card(
        rx.vstack(
            rx.heading(title, size="md"),
            rx.cond(
                len(items) > 0,
                rx.vstack(
                    rx.foreach(
                        items,
                        lambda item: rx.hstack(
                            rx.vstack(
                                rx.text(item[label_key], weight="bold", size="sm"),
                                rx.text(f"{item[value_key]}", size="xs", color="gray"),
                                align="start",
                                spacing="1"
                            ),
                            rx.spacer(),
                            rx.badge(f"{item[value_key]}", color_scheme="blue"),
                            width="100%",
                            align="center"
                        )
                    ),
                    spacing="3",
                    width="100%"
                ),
                rx.center(
                    rx.text("No data available", color="gray", size="sm"),
                    height="100px"
                )
            ),
            spacing="4",
            width="100%"
        ),
        width="100%"
    )


def date_range_selector() -> rx.Component:
    """Date range selector component."""
    return rx.hstack(
        rx.text("Date Range:", weight="bold", size="sm"),
        rx.select(
            ["today", "7_days", "30_days", "90_days", "custom"],
            value=AnalyticsState.date_range,
            on_change=AnalyticsState.set_date_range,
            placeholder="Select range"
        ),
        rx.cond(
            AnalyticsState.date_range == "custom",
            rx.hstack(
                rx.input(
                    type="date",
                    value=AnalyticsState.start_date,
                    on_change=AnalyticsState.set_start_date,
                    size="sm"
                ),
                rx.text("to", size="sm"),
                rx.input(
                    type="date",
                    value=AnalyticsState.end_date,
                    on_change=AnalyticsState.set_end_date,
                    size="sm"
                ),
                spacing="2"
            )
        ),
        rx.button(
            rx.icon("refresh-cw", size=16),
            "Refresh",
            on_click=AnalyticsState.load_analytics_data,
            size="sm",
            variant="outline"
        ),
        spacing="3",
        align="center"
    )


def analytics_page() -> rx.Component:
    """Analytics overview page."""
    return protected_layout(
        rx.vstack(
            page_header("Analytics", "View performance metrics and insights"),

            # Error message
            rx.cond(
                AnalyticsState.error_message != "",
                rx.callout(
                    AnalyticsState.error_message,
                    icon="alert-circle",
                    color_scheme="red",
                    width="100%"
                )
            ),

            # Date range selector
            date_range_selector(),

            # Real-time metrics
            rx.vstack(
                rx.heading("Real-time Metrics", size="lg"),
                rx.grid(
                    metric_card(
                        "Messages Today",
                        str(AnalyticsState.messages_today),
                        "Total messages sent",
                        "message-circle",
                        "blue"
                    ),
                    metric_card(
                        "Delivery Rate",
                        f"{AnalyticsState.delivery_rate_today}%",
                        "Today's delivery rate",
                        "check-circle",
                        "green"
                    ),
                    metric_card(
                        "Active Campaigns",
                        str(AnalyticsState.active_campaigns),
                        "Running campaigns",
                        "megaphone",
                        "orange"
                    ),
                    metric_card(
                        "Bot Tests",
                        str(AnalyticsState.bot_tests_today),
                        "Tests run today",
                        "bot",
                        "purple"
                    ),
                    columns="4",
                    spacing="4",
                    width="100%"
                ),
                spacing="4",
                width="100%"
            ),

            # Message Analytics
            rx.vstack(
                rx.heading("Message Analytics", size="lg"),
                rx.grid(
                    metric_card(
                        "Total Messages",
                        str(AnalyticsState.message_stats.get("total_messages", 0)),
                        f"Delivery rate: {AnalyticsState.message_stats.get('delivery_rate', 0)}%",
                        "send",
                        "blue"
                    ),
                    metric_card(
                        "Delivered",
                        str(AnalyticsState.message_stats.get("delivered_messages", 0)),
                        f"Read rate: {AnalyticsState.message_stats.get('read_rate', 0)}%",
                        "check",
                        "green"
                    ),
                    metric_card(
                        "Failed",
                        str(AnalyticsState.message_stats.get("failed_messages", 0)),
                        f"Failure rate: {AnalyticsState.message_stats.get('failure_rate', 0)}%",
                        "x-circle",
                        "red"
                    ),
                    columns="3",
                    spacing="4",
                    width="100%"
                ),
                spacing="4",
                width="100%"
            ),

            spacing="6",
            width="100%"
        ),
        current_page="analytics",
        on_mount=AnalyticsState.load_analytics_data
    )
