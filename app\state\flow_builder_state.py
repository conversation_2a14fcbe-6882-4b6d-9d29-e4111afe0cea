"""Flow builder state management."""

import reflex as rx
from typing import List, Dict, Any, Optional
import json
import uuid
from datetime import datetime

from ..models.whatsapp import WhatsAppFlow, WhatsAppAccount
from ..database import get_session
from .auth_state import AuthState


class FlowBuilderState(AuthState):
    """Flow builder state management."""
    
    # Flow management
    flows: List[Dict[str, Any]] = []
    current_flow: Optional[Dict[str, Any]] = None
    flow_id: Optional[str] = None
    
    # Flow properties
    flow_name: str = ""
    flow_description: str = ""
    flow_category: str = "SIGN_UP"
    flow_status: str = "DRAFT"
    
    # Flow structure
    flow_screens: List[Dict[str, Any]] = []
    current_screen: Optional[Dict[str, Any]] = None
    selected_screen_id: Optional[str] = None
    
    # Screen builder
    screen_name: str = ""
    screen_title: str = ""
    screen_data: List[Dict[str, Any]] = []
    
    # Component builder
    component_type: str = "TextInput"
    component_name: str = ""
    component_label: str = ""
    component_required: bool = False
    component_options: List[str] = []
    
    # Flow logic
    flow_actions: List[Dict[str, Any]] = []
    flow_conditions: List[Dict[str, Any]] = []
    
    # Preview and testing
    flow_preview_data: Dict[str, Any] = {}
    test_mode: bool = False
    test_results: List[Dict[str, Any]] = []
    
    # UI states
    loading: bool = False
    error_message: str = ""
    success_message: str = ""
    show_flow_builder: bool = False
    show_component_editor: bool = False
    
    # WhatsApp accounts
    whatsapp_accounts: List[Dict[str, Any]] = []
    selected_account_id: Optional[int] = None
    
    async def load_flows(self):
        """Load user's WhatsApp flows."""
        if not self.is_authenticated:
            return
        
        self.loading = True
        try:
            with get_session() as session:
                # Get user's WhatsApp accounts
                accounts = session.query(WhatsAppAccount).filter(
                    WhatsAppAccount.user_id == self.current_user["id"],
                    WhatsAppAccount.is_active == True
                ).all()
                
                self.whatsapp_accounts = [
                    {
                        "id": account.id,
                        "account_name": account.account_name,
                        "phone_number_id": account.phone_number_id,
                        "business_account_id": account.business_account_id
                    }
                    for account in accounts
                ]
                
                # Get flows for all accounts
                account_ids = [account.id for account in accounts]
                if account_ids:
                    flows = session.query(WhatsAppFlow).filter(
                        WhatsAppFlow.account_id.in_(account_ids)
                    ).order_by(WhatsAppFlow.created_at.desc()).all()
                    
                    self.flows = [
                        {
                            "id": flow.id,
                            "flow_id": flow.flow_id,
                            "name": flow.name,
                            "description": flow.description,
                            "category": flow.category,
                            "status": flow.status,
                            "account_id": flow.account_id,
                            "account_name": next(
                                (acc["account_name"] for acc in self.whatsapp_accounts 
                                 if acc["id"] == flow.account_id), 
                                "Unknown"
                            ),
                            "created_at": flow.created_at.isoformat() if flow.created_at else None,
                            "updated_at": flow.updated_at.isoformat() if flow.updated_at else None,
                            "flow_data": flow.flow_data or {}
                        }
                        for flow in flows
                    ]
                
        except Exception as e:
            self.error_message = f"Failed to load flows: {str(e)}"
        finally:
            self.loading = False
    
    def create_new_flow(self):
        """Create a new flow."""
        self.flow_id = str(uuid.uuid4())
        self.flow_name = ""
        self.flow_description = ""
        self.flow_category = "SIGN_UP"
        self.flow_status = "DRAFT"
        self.flow_screens = []
        self.current_screen = None
        self.selected_screen_id = None
        self.show_flow_builder = True
        self.clear_messages()
    
    def add_screen(self):
        """Add a new screen to the flow."""
        if not self.screen_name.strip() or not self.screen_title.strip():
            self.error_message = "Screen name and title are required"
            return
        
        screen_id = str(uuid.uuid4())
        new_screen = {
            "id": screen_id,
            "name": self.screen_name.strip(),
            "title": self.screen_title.strip(),
            "data": self.screen_data.copy(),
            "layout": {
                "type": "SingleColumnLayout",
                "children": []
            }
        }
        
        self.flow_screens.append(new_screen)
        self.current_screen = new_screen
        self.selected_screen_id = screen_id
        
        # Clear form
        self.screen_name = ""
        self.screen_title = ""
        self.screen_data = []
        
        self.success_message = f"Screen '{new_screen['title']}' added successfully"
        self.update_flow_preview()
    
    def select_screen(self, screen_id: str):
        """Select a screen for editing."""
        self.current_screen = next(
            (screen for screen in self.flow_screens if screen["id"] == screen_id),
            None
        )
        self.selected_screen_id = screen_id
        self.clear_messages()
    
    def add_component_to_screen(self):
        """Add a component to the current screen."""
        if not self.current_screen:
            self.error_message = "Please select a screen first"
            return
        
        if not self.component_name.strip() or not self.component_label.strip():
            self.error_message = "Component name and label are required"
            return
        
        component_id = str(uuid.uuid4())
        component = {
            "type": self.component_type,
            "name": self.component_name.strip(),
            "label": self.component_label.strip(),
            "required": self.component_required,
            "input-type": self._get_input_type()
        }
        
        # Add component-specific properties
        if self.component_type == "Dropdown":
            component["data-source"] = [
                {"id": f"option_{i}", "title": option.strip()}
                for i, option in enumerate(self.component_options)
                if option.strip()
            ]
        elif self.component_type == "RadioButtonsGroup":
            component["data-source"] = [
                {"id": f"radio_{i}", "title": option.strip()}
                for i, option in enumerate(self.component_options)
                if option.strip()
            ]
        elif self.component_type == "CheckboxGroup":
            component["data-source"] = [
                {"id": f"checkbox_{i}", "title": option.strip()}
                for i, option in enumerate(self.component_options)
                if option.strip()
            ]
        
        # Add to screen layout
        self.current_screen["layout"]["children"].append({
            "type": self.component_type,
            "props": component
        })
        
        # Clear component form
        self.component_name = ""
        self.component_label = ""
        self.component_required = False
        self.component_options = []
        
        self.success_message = f"Component '{component['label']}' added to screen"
        self.update_flow_preview()
    
    def _get_input_type(self) -> str:
        """Get input type based on component type."""
        input_types = {
            "TextInput": "text",
            "TextArea": "text",
            "DatePicker": "date",
            "Dropdown": "select",
            "RadioButtonsGroup": "radio",
            "CheckboxGroup": "checkbox"
        }
        return input_types.get(self.component_type, "text")
    
    def remove_component(self, screen_id: str, component_index: int):
        """Remove a component from a screen."""
        screen = next(
            (s for s in self.flow_screens if s["id"] == screen_id),
            None
        )
        
        if screen and 0 <= component_index < len(screen["layout"]["children"]):
            removed_component = screen["layout"]["children"].pop(component_index)
            self.success_message = f"Component removed from screen"
            self.update_flow_preview()
    
    def update_flow_preview(self):
        """Update flow preview data."""
        self.flow_preview_data = {
            "version": "5.0",
            "data_api_version": "3.0",
            "data_channel_uri": "https://your-webhook-url.com/flow",
            "routing_model": {
                "FLOW_START": self.flow_screens[0]["name"] if self.flow_screens else "START"
            },
            "screens": [
                {
                    "id": screen["name"],
                    "title": screen["title"],
                    "data": screen.get("data", []),
                    "layout": screen["layout"]
                }
                for screen in self.flow_screens
            ]
        }
    
    async def save_flow(self):
        """Save the current flow."""
        if not self.flow_name.strip():
            self.error_message = "Flow name is required"
            return
        
        if not self.selected_account_id:
            self.error_message = "Please select a WhatsApp account"
            return
        
        if not self.flow_screens:
            self.error_message = "Flow must have at least one screen"
            return
        
        self.loading = True
        try:
            self.update_flow_preview()
            
            with get_session() as session:
                # Check if updating existing flow
                if self.current_flow and self.current_flow.get("id"):
                    flow = session.get(WhatsAppFlow, self.current_flow["id"])
                    if flow:
                        flow.name = self.flow_name.strip()
                        flow.description = self.flow_description.strip()
                        flow.category = self.flow_category
                        flow.status = self.flow_status
                        flow.flow_data = self.flow_preview_data
                        flow.updated_at = datetime.utcnow()
                        
                        self.success_message = f"Flow '{self.flow_name}' updated successfully"
                else:
                    # Create new flow
                    new_flow = WhatsAppFlow(
                        account_id=self.selected_account_id,
                        flow_id=self.flow_id,
                        name=self.flow_name.strip(),
                        description=self.flow_description.strip(),
                        category=self.flow_category,
                        status=self.flow_status,
                        flow_data=self.flow_preview_data
                    )
                    
                    session.add(new_flow)
                    session.commit()
                    session.refresh(new_flow)
                    
                    self.current_flow = {
                        "id": new_flow.id,
                        "flow_id": new_flow.flow_id,
                        "name": new_flow.name,
                        "description": new_flow.description,
                        "category": new_flow.category,
                        "status": new_flow.status
                    }
                    
                    self.success_message = f"Flow '{self.flow_name}' created successfully"
                
                session.commit()
                
                # Reload flows
                await self.load_flows()
                
        except Exception as e:
            self.error_message = f"Failed to save flow: {str(e)}"
        finally:
            self.loading = False
    
    def load_flow_for_editing(self, flow_id: int):
        """Load a flow for editing."""
        flow = next(
            (f for f in self.flows if f["id"] == flow_id),
            None
        )
        
        if flow:
            self.current_flow = flow
            self.flow_id = flow["flow_id"]
            self.flow_name = flow["name"]
            self.flow_description = flow["description"]
            self.flow_category = flow["category"]
            self.flow_status = flow["status"]
            self.selected_account_id = flow["account_id"]
            
            # Load flow screens from flow_data
            flow_data = flow.get("flow_data", {})
            screens_data = flow_data.get("screens", [])
            
            self.flow_screens = [
                {
                    "id": str(uuid.uuid4()),
                    "name": screen.get("id", ""),
                    "title": screen.get("title", ""),
                    "data": screen.get("data", []),
                    "layout": screen.get("layout", {"type": "SingleColumnLayout", "children": []})
                }
                for screen in screens_data
            ]
            
            self.show_flow_builder = True
            self.update_flow_preview()
    
    def clear_messages(self):
        """Clear success and error messages."""
        self.error_message = ""
        self.success_message = ""
    
    def toggle_flow_builder(self):
        """Toggle flow builder visibility."""
        self.show_flow_builder = not self.show_flow_builder
        if not self.show_flow_builder:
            self.current_flow = None
            self.flow_screens = []
        self.clear_messages()
    
    def set_flow_name(self, name: str):
        """Set flow name."""
        self.flow_name = name
        self.clear_messages()
    
    def set_flow_description(self, description: str):
        """Set flow description."""
        self.flow_description = description
        self.clear_messages()
    
    def set_screen_name(self, name: str):
        """Set screen name."""
        self.screen_name = name
        self.clear_messages()
    
    def set_screen_title(self, title: str):
        """Set screen title."""
        self.screen_title = title
        self.clear_messages()
    
    def set_component_name(self, name: str):
        """Set component name."""
        self.component_name = name
        self.clear_messages()
    
    def set_component_label(self, label: str):
        """Set component label."""
        self.component_label = label
        self.clear_messages()
    
    def add_component_option(self, option: str):
        """Add option to component."""
        if option.strip() and option.strip() not in self.component_options:
            self.component_options.append(option.strip())
    
    def remove_component_option(self, index: int):
        """Remove option from component."""
        if 0 <= index < len(self.component_options):
            self.component_options.pop(index)
