# WhatsApp Business Platform 2.0

🚀 **Modern Web Application built with Python Reflex**

A comprehensive, professional-grade WhatsApp Business API management platform with modern web interface, user authentication, and advanced features.

## ✨ Features

### 🔐 **Authentication & User Management**
- User registration and login
- Role-based access control (Admin, Manager, User, Viewer)
- Session management and security
- Password reset functionality
- Multi-tenant support

### 💬 **Modern WhatsApp Business API v21.0**
- Interactive Button Messages
- Interactive List Messages  
- WhatsApp Flows (multi-step experiences)
- Message Templates
- Carousel Messages
- Location & Contact Messages
- Rich Media Support

### 🤖 **Advanced Bot Testing**
- JSON-based bot configurations
- Smart adaptive testing
- Response detection and validation
- Performance metrics and reporting
- Test scenario management

### 📊 **Analytics & Reporting**
- Real-time message analytics
- Campaign performance tracking
- Response rate monitoring
- Delivery and read receipts
- Custom dashboards

### 🎯 **Campaign Management**
- Bulk message campaigns
- Audience targeting
- Scheduled messaging
- A/B testing support
- Performance optimization

### 👥 **Team Collaboration**
- Multi-user workspace
- Permission management
- Shared templates and flows
- Team analytics
- Audit trails

## 🛠️ **Technology Stack**

- **Frontend**: Python Reflex (React-based)
- **Backend**: Python with SQLAlchemy
- **Database**: SQLite (dev) / PostgreSQL (prod)
- **Authentication**: JWT with bcrypt
- **Styling**: Tailwind CSS
- **Icons**: React Icons
- **Charts**: Recharts

## 🚀 **Quick Start**

### Prerequisites
- Python 3.8+
- Node.js 16+ (for Reflex frontend)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd whatsapp-business-platform
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Initialize the application**
```bash
reflex init
```

4. **Run the application**
```bash
python main.py
```

5. **Access the platform**
- Open http://localhost:3000
- Demo login: `<EMAIL>` / `demo123`
- Admin login: `<EMAIL>` / `admin123`

## 📱 **Application Structure**

```
app/
├── main.py                 # Main application entry
├── database.py            # Database configuration
├── models/                # Database models
│   ├── user.py           # User authentication
│   ├── whatsapp.py       # WhatsApp API models
│   ├── bot.py            # Bot testing models
│   └── analytics.py      # Analytics models
├── pages/                 # Application pages
│   ├── auth.py           # Login/Register pages
│   ├── dashboard.py      # Main dashboard
│   ├── messages.py       # Message management
│   ├── campaigns.py      # Campaign management
│   ├── templates.py      # Template management
│   ├── flows.py          # Flow builder
│   ├── bot_testing.py    # Bot testing interface
│   ├── analytics.py      # Analytics dashboard
│   ├── settings.py       # User settings
│   └── admin.py          # Admin panel
├── components/           # Reusable components
│   ├── layout.py         # Layout components
│   ├── navigation.py     # Navigation components
│   ├── forms.py          # Form components
│   └── charts.py         # Chart components
├── state/                # Application state
│   └── auth_state.py     # Authentication state
└── utils/                # Utility functions
    └── security.py       # Security utilities
```

## 🔧 **Configuration**

### Environment Variables
Create a `.env` file:

```env
# Database
DATABASE_URL=sqlite:///whatsapp_platform.db

# Security
SECRET_KEY=your-super-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# WhatsApp Business API
WHATSAPP_API_VERSION=v21.0
WHATSAPP_BASE_URL=https://graph.facebook.com

# Email (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### WhatsApp Business API Setup
1. Create a Meta Developer account
2. Set up WhatsApp Business API
3. Get your credentials:
   - Phone Number ID
   - Access Token
   - Business Account ID
   - Webhook Verify Token

## 🎨 **User Interface**

### Modern Design
- Clean, professional interface
- Responsive design for all devices
- Dark/light theme support
- Intuitive navigation
- Real-time updates

### Key Pages
- **Dashboard**: Overview and quick actions
- **Messages**: Compose and send messages
- **Campaigns**: Manage marketing campaigns
- **Templates**: Create message templates
- **Flows**: Build interactive experiences
- **Bot Testing**: Test and validate bots
- **Analytics**: Performance insights
- **Settings**: Account and API configuration

## 🔒 **Security Features**

- JWT-based authentication
- Password hashing with bcrypt
- Session management
- Role-based access control
- Input validation and sanitization
- CSRF protection
- Rate limiting
- Audit logging

## 📊 **Analytics & Monitoring**

- Message delivery tracking
- Response rate analysis
- Campaign performance metrics
- Bot testing results
- User activity monitoring
- System health checks

## 🚀 **Deployment**

### Development
```bash
python main.py
```

### Production
```bash
# Using gunicorn
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app

# Using Docker
docker build -t whatsapp-platform .
docker run -p 8000:8000 whatsapp-platform
```

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 **Support**

- Documentation: [docs.whatsappplatform.com](https://docs.whatsappplatform.com)
- Issues: [GitHub Issues](https://github.com/your-org/whatsapp-business-platform/issues)
- Email: <EMAIL>

## 🎯 **Roadmap**

- [ ] Real-time webhook integration
- [ ] Advanced flow builder with drag-and-drop
- [ ] AI-powered message optimization
- [ ] Multi-language support
- [ ] Advanced analytics with ML insights
- [ ] Integration with CRM systems
- [ ] Mobile app for iOS/Android
- [ ] API for third-party integrations

---

**Built with ❤️ using Python Reflex**
