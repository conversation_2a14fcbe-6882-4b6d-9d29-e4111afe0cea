"""Analytics and reporting models."""

import reflex as rx
from sqlmodel import Field
from sqlalchemy import Column, String, Integer, ForeignKey, DateTime, JSON, Float, <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any
from .base import BaseModel


class MessageAnalytics(BaseModel, table=True):
    """Message analytics and metrics."""
    
    message_id: int = Column(Integer, ForeignKey("message.id"), nullable=False)
    account_id: int = Column(Integer, ForeignKey("whatsappaccount.id"), nullable=False)
    
    # Metrics
    delivery_time: Optional[float] = Column(Float, nullable=True)  # seconds
    read_time: Optional[float] = Column(Float, nullable=True)  # seconds
    response_received: bool = Column(Boolean, default=False)
    response_time: Optional[float] = Column(Float, nullable=True)  # seconds
    
    # Additional analytics data
    analytics_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel


class CampaignAnalytics(BaseModel, table=True):
    """Campaign analytics and performance metrics."""
    
    campaign_id: int = Column(Integer, ForeignKey("campaign.id"), nullable=False)
    
    # Campaign metrics
    total_recipients: int = Column(Integer, default=0)
    messages_sent: int = Column(Integer, default=0)
    messages_delivered: int = Column(Integer, default=0)
    messages_read: int = Column(Integer, default=0)
    responses_received: int = Column(Integer, default=0)
    
    # Performance metrics
    delivery_rate: float = Column(Float, default=0.0)
    read_rate: float = Column(Float, default=0.0)
    response_rate: float = Column(Float, default=0.0)
    
    # Timing metrics
    average_delivery_time: Optional[float] = Column(Float, nullable=True)
    average_read_time: Optional[float] = Column(Float, nullable=True)
    average_response_time: Optional[float] = Column(Float, nullable=True)
    
    # Cost metrics
    total_cost: Optional[float] = Column(Float, nullable=True)
    cost_per_message: Optional[float] = Column(Float, nullable=True)
    
    # Additional analytics
    analytics_data: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON))
    
    # Note: Relationships are handled by SQLAlchemy, not defined as fields in SQLModel
